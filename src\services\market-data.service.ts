import type { MarketDataResponse, CityData, MaterialCost, LaborCost } from '@/types/calculator';

export class MarketDataService {
  private cache: Map<string, { data: MarketDataResponse; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours
  private readonly API_BASE_URL = process.env.VITE_API_BASE_URL || 'https://api.constructcalc.com';

  // Fallback data for offline functionality
  private readonly FALLBACK_DATA: Record<string, CityData> = {
    delhi: {
      id: 'delhi',
      name: 'Delhi NCR',
      country: 'India',
      region: 'asia-south',
      materials: [
        { id: 'cement', name: 'Cement', emoji: '🧱', costPerSqft: 50, unit: 'kg', category: 'structural' },
        { id: 'steel', name: 'Steel', emoji: '🔩', costPerSqft: 200, unit: 'kg', category: 'structural' },
        { id: 'bricks', name: 'Bricks', emoji: '🧱', costPerSqft: 30, unit: 'piece', category: 'masonry' },
        { id: 'tiles', name: 'Tiles', emoji: '🪨', costPerSqft: 80, unit: 'sqft', category: 'finishing' },
        { id: 'sand', name: 'Sand', emoji: '🏖️', costPerSqft: 20, unit: 'cft', category: 'raw' },
        { id: 'paint', name: 'Paint', emoji: '🎨', costPerSqft: 40, unit: 'liter', category: 'finishing' },
        { id: 'plumbing', name: 'Plumbing Materials', emoji: '🚰', costPerSqft: 60, unit: 'set', category: 'utilities' },
        { id: 'electrical', name: 'Electrical Materials', emoji: '💡', costPerSqft: 70, unit: 'set', category: 'utilities' },
        { id: 'wood', name: 'Wood', emoji: '🪵', costPerSqft: 90, unit: 'cft', category: 'finishing' },
        { id: 'glass', name: 'Glass', emoji: '🪟', costPerSqft: 100, unit: 'sqft', category: 'finishing' }
      ],
      labor: [
        { id: 'painter', name: 'Painter', emoji: '🎨', costPerSqft: 30, skillLevel: 'skilled' },
        { id: 'carpenter', name: 'Carpenter', emoji: '🔨', costPerSqft: 50, skillLevel: 'skilled' },
        { id: 'electrician', name: 'Electrician', emoji: '🔌', costPerSqft: 60, skillLevel: 'expert' },
        { id: 'plumber', name: 'Plumber', emoji: '🚿', costPerSqft: 40, skillLevel: 'skilled' },
        { id: 'mason', name: 'Mason', emoji: '🧱', costPerSqft: 70, skillLevel: 'skilled' },
        { id: 'laborer', name: 'General Laborer', emoji: '👷', costPerSqft: 20, skillLevel: 'basic' }
      ],
      baseRates: {
        standard: 900,
        premium: 1100,
        luxury: 1300
      },
      regulations: {
        minSetback: 3,
        maxHeight: 15,
        farRatio: 1.5
      }
    },
    mumbai: {
      id: 'mumbai',
      name: 'Mumbai',
      country: 'India',
      region: 'asia-south',
      materials: [
        { id: 'cement', name: 'Cement', emoji: '🧱', costPerSqft: 55, unit: 'kg', category: 'structural' },
        { id: 'steel', name: 'Steel', emoji: '🔩', costPerSqft: 205, unit: 'kg', category: 'structural' },
        { id: 'bricks', name: 'Bricks', emoji: '🧱', costPerSqft: 35, unit: 'piece', category: 'masonry' },
        { id: 'tiles', name: 'Tiles', emoji: '🪨', costPerSqft: 85, unit: 'sqft', category: 'finishing' },
        { id: 'sand', name: 'Sand', emoji: '🏖️', costPerSqft: 25, unit: 'cft', category: 'raw' },
        { id: 'paint', name: 'Paint', emoji: '🎨', costPerSqft: 45, unit: 'liter', category: 'finishing' },
        { id: 'plumbing', name: 'Plumbing Materials', emoji: '🚰', costPerSqft: 65, unit: 'set', category: 'utilities' },
        { id: 'electrical', name: 'Electrical Materials', emoji: '💡', costPerSqft: 75, unit: 'set', category: 'utilities' },
        { id: 'wood', name: 'Wood', emoji: '🪵', costPerSqft: 95, unit: 'cft', category: 'finishing' },
        { id: 'glass', name: 'Glass', emoji: '🪟', costPerSqft: 105, unit: 'sqft', category: 'finishing' }
      ],
      labor: [
        { id: 'painter', name: 'Painter', emoji: '🎨', costPerSqft: 35, skillLevel: 'skilled' },
        { id: 'carpenter', name: 'Carpenter', emoji: '🔨', costPerSqft: 55, skillLevel: 'skilled' },
        { id: 'electrician', name: 'Electrician', emoji: '🔌', costPerSqft: 65, skillLevel: 'expert' },
        { id: 'plumber', name: 'Plumber', emoji: '🚿', costPerSqft: 45, skillLevel: 'skilled' },
        { id: 'mason', name: 'Mason', emoji: '🧱', costPerSqft: 75, skillLevel: 'skilled' },
        { id: 'laborer', name: 'General Laborer', emoji: '👷', costPerSqft: 25, skillLevel: 'basic' }
      ],
      baseRates: {
        standard: 950,
        premium: 1200,
        luxury: 1500
      },
      regulations: {
        minSetback: 2,
        maxHeight: 20,
        farRatio: 2.0
      }
    },
    bangalore: {
      id: 'bangalore',
      name: 'Bangalore',
      country: 'India',
      region: 'asia-south',
      materials: [
        { id: 'cement', name: 'Cement', emoji: '🧱', costPerSqft: 60, unit: 'kg', category: 'structural' },
        { id: 'steel', name: 'Steel', emoji: '🔩', costPerSqft: 210, unit: 'kg', category: 'structural' },
        { id: 'bricks', name: 'Bricks', emoji: '🧱', costPerSqft: 40, unit: 'piece', category: 'masonry' },
        { id: 'tiles', name: 'Tiles', emoji: '🪨', costPerSqft: 80, unit: 'sqft', category: 'finishing' },
        { id: 'sand', name: 'Sand', emoji: '🏖️', costPerSqft: 20, unit: 'cft', category: 'raw' },
        { id: 'paint', name: 'Paint', emoji: '🎨', costPerSqft: 40, unit: 'liter', category: 'finishing' },
        { id: 'plumbing', name: 'Plumbing Materials', emoji: '🚰', costPerSqft: 60, unit: 'set', category: 'utilities' },
        { id: 'electrical', name: 'Electrical Materials', emoji: '💡', costPerSqft: 70, unit: 'set', category: 'utilities' },
        { id: 'wood', name: 'Wood', emoji: '🪵', costPerSqft: 90, unit: 'cft', category: 'finishing' },
        { id: 'glass', name: 'Glass', emoji: '🪟', costPerSqft: 100, unit: 'sqft', category: 'finishing' }
      ],
      labor: [
        { id: 'painter', name: 'Painter', emoji: '🎨', costPerSqft: 30, skillLevel: 'skilled' },
        { id: 'carpenter', name: 'Carpenter', emoji: '🔨', costPerSqft: 50, skillLevel: 'skilled' },
        { id: 'electrician', name: 'Electrician', emoji: '🔌', costPerSqft: 60, skillLevel: 'expert' },
        { id: 'plumber', name: 'Plumber', emoji: '🚿', costPerSqft: 40, skillLevel: 'skilled' },
        { id: 'mason', name: 'Mason', emoji: '🧱', costPerSqft: 70, skillLevel: 'skilled' },
        { id: 'laborer', name: 'General Laborer', emoji: '👷', costPerSqft: 20, skillLevel: 'basic' }
      ],
      baseRates: {
        standard: 850,
        premium: 1050,
        luxury: 1250
      },
      regulations: {
        minSetback: 3,
        maxHeight: 18,
        farRatio: 1.75
      }
    }
  };

  async getMarketData(city: string): Promise<MarketDataResponse> {
    // Check cache first
    const cached = this.cache.get(city);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data;
    }

    try {
      // Try to fetch from API
      const response = await fetch(`${this.API_BASE_URL}/market-data/${city}`, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data: MarketDataResponse = await response.json();
        
        // Cache the response
        this.cache.set(city, {
          data,
          timestamp: Date.now()
        });

        return data;
      }
    } catch (error) {
      console.warn('Failed to fetch market data from API:', error);
    }

    // Fallback to local data
    return this.getFallbackData(city);
  }

  private getFallbackData(city: string): MarketDataResponse {
    const cityData = this.FALLBACK_DATA[city.toLowerCase()] || this.FALLBACK_DATA.delhi;
    
    return {
      city: cityData.name,
      date: new Date().toISOString().split('T')[0],
      materials: cityData.materials,
      labor: cityData.labor,
      trends: {
        material: 'stable',
        labor: 'stable',
        percentage: 0
      }
    };
  }

  async getAllCities(): Promise<CityData[]> {
    try {
      const response = await fetch(`${this.API_BASE_URL}/cities`);
      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      console.warn('Failed to fetch cities from API:', error);
    }

    // Return fallback cities
    return Object.values(this.FALLBACK_DATA);
  }

  async getCityData(cityId: string): Promise<CityData | null> {
    try {
      const response = await fetch(`${this.API_BASE_URL}/cities/${cityId}`);
      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      console.warn('Failed to fetch city data from API:', error);
    }

    // Return fallback data
    return this.FALLBACK_DATA[cityId.toLowerCase()] || null;
  }

  // Get material price trends
  async getMaterialTrends(city: string, materialId: string, days: number = 30): Promise<any[]> {
    try {
      const response = await fetch(
        `${this.API_BASE_URL}/trends/${city}/${materialId}?days=${days}`
      );
      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      console.warn('Failed to fetch material trends:', error);
    }

    // Return mock trend data
    return this.generateMockTrends(days);
  }

  private generateMockTrends(days: number): any[] {
    const trends = [];
    const basePrice = 100;
    
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() - (days - i));
      
      const variation = (Math.random() - 0.5) * 10; // ±5% variation
      const price = basePrice + variation;
      
      trends.push({
        date: date.toISOString().split('T')[0],
        price: Math.round(price * 100) / 100
      });
    }
    
    return trends;
  }

  // Clear cache
  clearCache(): void {
    this.cache.clear();
  }

  // Get cache status
  getCacheStatus(): { city: string; age: number }[] {
    const status: { city: string; age: number }[] = [];
    
    this.cache.forEach((value, key) => {
      status.push({
        city: key,
        age: Date.now() - value.timestamp
      });
    });
    
    return status;
  }

  // Preload data for multiple cities
  async preloadCities(cities: string[]): Promise<void> {
    const promises = cities.map(city => this.getMarketData(city));
    await Promise.allSettled(promises);
  }
}
