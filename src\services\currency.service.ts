import type { Currency, CurrencyRateResponse } from '@/types/calculator';

export class CurrencyService {
  private rates: Map<string, number> = new Map();
  private lastUpdated: Date | null = null;
  private readonly CACHE_DURATION = 60 * 60 * 1000; // 1 hour in milliseconds
  private readonly API_ENDPOINTS = [
    'https://api.exchangerate-api.com/v4/latest/INR',
    'https://api.fixer.io/latest?base=INR',
    'https://openexchangerates.org/api/latest.json?base=INR'
  ];

  // Fallback rates (updated periodically)
  private readonly FALLBACK_RATES: Record<Currency, number> = {
    INR: 1,
    USD: 0.012,
    EUR: 0.011,
    GBP: 0.0096,
    CAD: 0.016,
    AUD: 0.018,
    JPY: 1.8
  };

  constructor() {
    this.initializeRates();
  }

  private async initializeRates(): Promise<void> {
    try {
      await this.updateRates();
    } catch (error) {
      console.warn('Failed to fetch live rates, using fallback rates:', error);
      this.loadFallbackRates();
    }
  }

  public async updateRates(): Promise<void> {
    // Check if rates are still fresh
    if (this.lastUpdated && 
        Date.now() - this.lastUpdated.getTime() < this.CACHE_DURATION) {
      return;
    }

    for (const endpoint of this.API_ENDPOINTS) {
      try {
        const response = await fetch(endpoint);
        if (!response.ok) continue;

        const data: CurrencyRateResponse = await response.json();
        
        // Store rates
        Object.entries(data.rates).forEach(([currency, rate]) => {
          this.rates.set(currency, rate);
        });

        this.lastUpdated = new Date();
        
        // Cache rates in localStorage
        this.cacheRates(data.rates);
        
        console.log('Currency rates updated successfully');
        return;

      } catch (error) {
        console.warn(`Failed to fetch from ${endpoint}:`, error);
        continue;
      }
    }

    // If all APIs fail, try to load from cache
    const cachedRates = this.loadCachedRates();
    if (cachedRates) {
      Object.entries(cachedRates).forEach(([currency, rate]) => {
        this.rates.set(currency, rate);
      });
      console.log('Loaded currency rates from cache');
    } else {
      // Final fallback
      this.loadFallbackRates();
      console.log('Using fallback currency rates');
    }
  }

  private loadFallbackRates(): void {
    Object.entries(this.FALLBACK_RATES).forEach(([currency, rate]) => {
      this.rates.set(currency, rate);
    });
    this.lastUpdated = new Date();
  }

  private cacheRates(rates: Record<string, number>): void {
    try {
      const cacheData = {
        rates,
        timestamp: Date.now()
      };
      localStorage.setItem('currency_rates', JSON.stringify(cacheData));
    } catch (error) {
      console.warn('Failed to cache currency rates:', error);
    }
  }

  private loadCachedRates(): Record<string, number> | null {
    try {
      const cached = localStorage.getItem('currency_rates');
      if (!cached) return null;

      const cacheData = JSON.parse(cached);
      
      // Check if cache is still valid (24 hours)
      if (Date.now() - cacheData.timestamp > 24 * 60 * 60 * 1000) {
        return null;
      }

      return cacheData.rates;
    } catch (error) {
      console.warn('Failed to load cached rates:', error);
      return null;
    }
  }

  public async convert(amount: number, from: Currency, to: Currency): Promise<number> {
    if (from === to) return amount;

    // Ensure rates are up to date
    await this.updateRates();

    const fromRate = this.rates.get(from) || this.FALLBACK_RATES[from];
    const toRate = this.rates.get(to) || this.FALLBACK_RATES[to];

    if (!fromRate || !toRate) {
      throw new Error(`Currency conversion not available for ${from} to ${to}`);
    }

    // Convert via INR (base currency)
    const inrAmount = amount / fromRate;
    return inrAmount * toRate;
  }

  public convertSync(amount: number, from: Currency, to: Currency): number {
    if (from === to) return amount;

    const fromRate = this.rates.get(from) || this.FALLBACK_RATES[from];
    const toRate = this.rates.get(to) || this.FALLBACK_RATES[to];

    if (!fromRate || !toRate) {
      console.warn(`Currency conversion not available for ${from} to ${to}, using fallback`);
      return amount; // Return original amount as fallback
    }

    // Convert via INR (base currency)
    const inrAmount = amount / fromRate;
    return inrAmount * toRate;
  }

  public getRate(currency: Currency): number {
    return this.rates.get(currency) || this.FALLBACK_RATES[currency];
  }

  public getAllRates(): Record<Currency, number> {
    const result: Partial<Record<Currency, number>> = {};
    
    Object.keys(this.FALLBACK_RATES).forEach(currency => {
      const curr = currency as Currency;
      result[curr] = this.rates.get(curr) || this.FALLBACK_RATES[curr];
    });

    return result as Record<Currency, number>;
  }

  public getLastUpdated(): Date | null {
    return this.lastUpdated;
  }

  public formatCurrency(amount: number, currency: Currency, locale?: string): string {
    const detectedLocale = locale || navigator.language || 'en-US';
    
    try {
      return new Intl.NumberFormat(detectedLocale, {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(amount);
    } catch (error) {
      // Fallback formatting
      const symbols: Record<Currency, string> = {
        INR: '₹',
        USD: '$',
        EUR: '€',
        GBP: '£',
        CAD: 'C$',
        AUD: 'A$',
        JPY: '¥'
      };
      
      return `${symbols[currency]} ${amount.toLocaleString()}`;
    }
  }

  public getSupportedCurrencies(): Currency[] {
    return Object.keys(this.FALLBACK_RATES) as Currency[];
  }

  public isValidCurrency(currency: string): currency is Currency {
    return Object.keys(this.FALLBACK_RATES).includes(currency);
  }

  // Get currency symbol
  public getCurrencySymbol(currency: Currency): string {
    const symbols: Record<Currency, string> = {
      INR: '₹',
      USD: '$',
      EUR: '€',
      GBP: '£',
      CAD: 'C$',
      AUD: 'A$',
      JPY: '¥'
    };
    
    return symbols[currency] || currency;
  }

  // Get currency name
  public getCurrencyName(currency: Currency): string {
    const names: Record<Currency, string> = {
      INR: 'Indian Rupee',
      USD: 'US Dollar',
      EUR: 'Euro',
      GBP: 'British Pound',
      CAD: 'Canadian Dollar',
      AUD: 'Australian Dollar',
      JPY: 'Japanese Yen'
    };
    
    return names[currency] || currency;
  }
}
