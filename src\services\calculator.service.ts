import type { 
  CalculatorConfig, 
  CostBreakdown, 
  TimeEstimate, 
  CalculationResult,
  ValidationError,
  PlotDimensions,
  Room
} from '@/types/calculator';
import { ValidationService } from './validation.service';
import { CurrencyService } from './currency.service';
import { MarketDataService } from './market-data.service';
import { debounce } from 'lodash-es';

export class CalculatorService {
  private config: CalculatorConfig;
  private validationService: ValidationService;
  private currencyService: CurrencyService;
  private marketDataService: MarketDataService;

  constructor(config: CalculatorConfig) {
    this.config = config;
    this.validationService = new ValidationService();
    this.currencyService = new CurrencyService();
    this.marketDataService = new MarketDataService();
  }

  // Debounced calculation for real-time updates
  public calculateDebounced = debounce(this.calculate.bind(this), 300);

  public async calculate(): Promise<CalculationResult> {
    try {
      // Validate inputs
      const validationErrors = await this.validateInputs();
      if (validationErrors.length > 0) {
        return {
          isValid: false,
          errors: validationErrors
        };
      }

      // Get market data
      const marketData = await this.marketDataService.getMarketData(this.config.city);
      
      // Calculate costs
      const costBreakdown = await this.calculateCosts(marketData);
      
      // Calculate time estimate
      const timeEstimate = this.calculateTimeEstimate();
      
      // Generate recommendations
      const recommendations = this.generateRecommendations();

      return {
        isValid: true,
        errors: [],
        costBreakdown,
        timeEstimate,
        recommendations
      };

    } catch (error) {
      console.error('Calculation error:', error);
      return {
        isValid: false,
        errors: [{
          field: 'general',
          message: 'Calculation failed. Please try again.',
          code: 'CALCULATION_ERROR'
        }]
      };
    }
  }

  private async validateInputs(): Promise<ValidationError[]> {
    const errors: ValidationError[] = [];

    // Validate dimensions
    const dimensionErrors = this.validationService.validateDimensions(this.config.dimensions);
    errors.push(...dimensionErrors);

    // Validate rooms
    const roomErrors = this.validationService.validateRooms(this.config.rooms, this.config.dimensions);
    errors.push(...roomErrors);

    // Validate city and currency
    if (!this.config.city) {
      errors.push({
        field: 'city',
        message: 'Please select a city',
        code: 'REQUIRED_FIELD'
      });
    }

    if (!this.config.currency) {
      errors.push({
        field: 'currency',
        message: 'Please select a currency',
        code: 'REQUIRED_FIELD'
      });
    }

    return errors;
  }

  private async calculateCosts(marketData: any): Promise<CostBreakdown> {
    const totalArea = this.calculateTotalArea();
    const qualityMultiplier = this.getQualityMultiplier();
    
    // Calculate material costs
    const materialCosts = marketData.materials.map((material: any) => ({
      ...material,
      total: material.costPerSqft * totalArea * qualityMultiplier
    }));

    // Calculate labor costs
    const laborCosts = marketData.labor.map((labor: any) => ({
      ...labor,
      total: labor.costPerSqft * totalArea * qualityMultiplier
    }));

    const materialSubtotal = materialCosts.reduce((sum: number, item: any) => sum + item.total, 0);
    const laborSubtotal = laborCosts.reduce((sum: number, item: any) => sum + item.total, 0);
    const total = materialSubtotal + laborSubtotal;

    // Convert to selected currency
    const convertedTotal = await this.currencyService.convert(total, 'INR', this.config.currency);
    const convertedMaterialSubtotal = await this.currencyService.convert(materialSubtotal, 'INR', this.config.currency);
    const convertedLaborSubtotal = await this.currencyService.convert(laborSubtotal, 'INR', this.config.currency);

    return {
      materials: {
        items: materialCosts.map((item: any) => ({
          ...item,
          total: this.currencyService.convertSync(item.total, 'INR', this.config.currency)
        })),
        subtotal: convertedMaterialSubtotal
      },
      labor: {
        items: laborCosts.map((item: any) => ({
          ...item,
          total: this.currencyService.convertSync(item.total, 'INR', this.config.currency)
        })),
        subtotal: convertedLaborSubtotal
      },
      total: convertedTotal,
      currency: this.config.currency,
      quality: this.config.quality,
      area: totalArea,
      pricePerSqft: convertedTotal / totalArea
    };
  }

  private calculateTimeEstimate(): TimeEstimate {
    const totalArea = this.calculateTotalArea();
    const qualityMultiplier = this.getQualityMultiplier();
    
    // Base time factors (days per 1000 sqft)
    const baseTimeFactors = {
      standard: 60,
      premium: 90,
      luxury: 120
    };

    const baseDays = (totalArea / 1000) * baseTimeFactors[this.config.quality];
    const totalDays = Math.ceil(baseDays * qualityMultiplier);

    // Define construction phases
    const phases = [
      { name: 'Planning & Permits', duration: Math.ceil(totalDays * 0.1), dependencies: [] },
      { name: 'Foundation', duration: Math.ceil(totalDays * 0.15), dependencies: ['Planning & Permits'] },
      { name: 'Structure', duration: Math.ceil(totalDays * 0.25), dependencies: ['Foundation'] },
      { name: 'Roofing', duration: Math.ceil(totalDays * 0.1), dependencies: ['Structure'] },
      { name: 'Electrical', duration: Math.ceil(totalDays * 0.15), dependencies: ['Structure'] },
      { name: 'Plumbing', duration: Math.ceil(totalDays * 0.15), dependencies: ['Structure'] },
      { name: 'Flooring', duration: Math.ceil(totalDays * 0.1), dependencies: ['Electrical', 'Plumbing'] },
      { name: 'Finishing', duration: Math.ceil(totalDays * 0.2), dependencies: ['Flooring'] }
    ];

    return {
      phases,
      totalDays,
      totalMonths: Math.ceil(totalDays / 30),
      totalYears: Math.floor(totalDays / 365),
      criticalPath: ['Planning & Permits', 'Foundation', 'Structure', 'Finishing']
    };
  }

  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    const totalArea = this.calculateTotalArea();
    const plotArea = this.calculatePlotArea();
    const efficiency = (totalArea / plotArea) * 100;

    // Space efficiency recommendations
    if (efficiency < 60) {
      recommendations.push('Consider adding more rooms to improve space utilization');
    } else if (efficiency > 90) {
      recommendations.push('Consider reducing room sizes to avoid overcrowding');
    }

    // Essential rooms check
    const essentialRooms = ['bedroom', 'bathroom', 'kitchen', 'living-room'];
    const selectedRoomTypes = this.config.rooms.map(room => room.id);
    const missingEssential = essentialRooms.filter(room => !selectedRoomTypes.includes(room));
    
    if (missingEssential.length > 0) {
      recommendations.push(`Consider adding essential rooms: ${missingEssential.join(', ')}`);
    }

    // Quality vs budget recommendations
    if (this.config.quality === 'luxury' && totalArea > 3000) {
      recommendations.push('Large luxury construction may require extended timeline and budget');
    }

    // Eco-friendly suggestions
    const ecoRooms = this.config.rooms.filter(room => room.category === 'eco');
    if (ecoRooms.length === 0) {
      recommendations.push('Consider adding eco-friendly features for sustainability');
    }

    return recommendations;
  }

  private calculateTotalArea(): number {
    return this.config.rooms.reduce((total, room) => total + room.area, 0);
  }

  private calculatePlotArea(): number {
    const { length, width, floors } = this.config.dimensions;
    return length * width * floors;
  }

  private getQualityMultiplier(): number {
    const multipliers = {
      standard: 1.0,
      premium: 1.25,
      luxury: 1.5
    };
    return multipliers[this.config.quality];
  }

  // Public methods for updating configuration
  public updateDimensions(dimensions: PlotDimensions): void {
    this.config.dimensions = dimensions;
  }

  public updateRooms(rooms: Room[]): void {
    this.config.rooms = rooms;
  }

  public updateQuality(quality: typeof this.config.quality): void {
    this.config.quality = quality;
  }

  public updateCity(city: string): void {
    this.config.city = city;
  }

  public updateCurrency(currency: typeof this.config.currency): void {
    this.config.currency = currency;
  }

  public getConfig(): CalculatorConfig {
    return { ...this.config };
  }
}
