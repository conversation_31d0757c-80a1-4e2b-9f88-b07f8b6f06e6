/* Modern CSS Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--bg-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
  min-height: 100vh;
  overflow-x: hidden;
}

/* CSS Custom Properties (Design Tokens) */
:root {
  /* Colors - Light Theme */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Semantic Colors */
  --bg-primary: #ffffff;
  --bg-secondary: var(--gray-50);
  --bg-tertiary: var(--gray-100);
  --bg-hover: var(--gray-100);
  --bg-focus: var(--primary-50);
  --bg-selected: var(--primary-100);

  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-600);
  --text-muted: var(--gray-500);
  --text-inverse: #ffffff;

  --border-color: var(--gray-200);
  --border-hover: var(--gray-300);
  --border-focus: var(--primary-500);

  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Status Colors */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: var(--primary-500);

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;

  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-normal: 250ms ease;
  --transition-slow: 350ms ease;

  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
}

/* Dark Theme */
.dark-theme {
  --bg-primary: var(--gray-900);
  --bg-secondary: var(--gray-800);
  --bg-tertiary: var(--gray-700);
  --bg-hover: var(--gray-700);
  --bg-focus: var(--gray-800);
  --bg-selected: var(--gray-700);

  --text-primary: var(--gray-100);
  --text-secondary: var(--gray-300);
  --text-muted: var(--gray-400);
  --text-inverse: var(--gray-900);

  --border-color: var(--gray-700);
  --border-hover: var(--gray-600);
  --border-focus: var(--primary-400);

  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);
}

/* High Contrast Mode */
.high-contrast {
  --bg-primary: #000000;
  --bg-secondary: #000000;
  --bg-tertiary: #000000;
  --bg-hover: #333333;
  --bg-focus: #0066cc;
  --bg-selected: #0066cc;

  --text-primary: #ffffff;
  --text-secondary: #ffffff;
  --text-muted: #cccccc;
  --text-inverse: #000000;

  --border-color: #ffffff;
  --border-hover: #ffffff;
  --border-focus: #ffff00;

  --primary-500: #0066cc;
  --success-color: #00ff00;
  --warning-color: #ffff00;
  --error-color: #ff0000;
}

/* Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  transition: opacity 0.3s ease;
}

.loading-container {
  text-align: center;
  max-width: 400px;
  padding: var(--space-8);
}

.loading-logo {
  margin-bottom: var(--space-8);
}

.logo-icon {
  font-size: var(--font-size-4xl);
  display: block;
  margin-bottom: var(--space-4);
}

.logo-text {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.loading-spinner {
  margin: var(--space-8) 0;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-500);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-message {
  color: var(--text-secondary);
  margin-bottom: var(--space-4);
}

.loading-progress {
  width: 100%;
  height: 4px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: var(--primary-500);
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
  width: 0%;
}

/* Error Screen */
.error-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.error-container {
  text-align: center;
  max-width: 500px;
  padding: var(--space-8);
}

.error-icon {
  font-size: 4rem;
  margin-bottom: var(--space-6);
}

.error-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.error-message {
  color: var(--text-secondary);
  margin-bottom: var(--space-8);
  font-size: var(--font-size-lg);
}

.error-retry-btn {
  background: var(--primary-500);
  color: var(--text-inverse);
  border: none;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.error-retry-btn:hover {
  background: var(--primary-600);
}

.error-retry-btn:focus {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

/* PWA Install Prompt */
.pwa-install-prompt {
  position: fixed;
  bottom: var(--space-4);
  left: var(--space-4);
  right: var(--space-4);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-fixed);
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.prompt-content {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
}

.prompt-icon {
  font-size: var(--font-size-xl);
}

.prompt-text {
  flex: 1;
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.prompt-install-btn {
  background: var(--primary-500);
  color: var(--text-inverse);
  border: none;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.prompt-install-btn:hover {
  background: var(--primary-600);
}

.prompt-close-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-sm);
  transition: color var(--transition-fast);
}

.prompt-close-btn:hover {
  color: var(--text-primary);
}

/* Offline Indicator */
.offline-indicator {
  position: fixed;
  top: var(--space-4);
  left: 50%;
  transform: translateX(-50%);
  background: var(--warning-color);
  color: var(--text-inverse);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  z-index: var(--z-fixed);
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    transform: translateX(-50%) translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}

.offline-icon {
  margin-right: var(--space-2);
}

/* Update Notification */
.update-notification {
  position: fixed;
  top: var(--space-4);
  right: var(--space-4);
  background: var(--info-color);
  color: var(--text-inverse);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-fixed);
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.notification-content {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
}

.notification-icon {
  font-size: var(--font-size-lg);
}

.notification-text {
  flex: 1;
  font-weight: var(--font-weight-medium);
}

.notification-btn {
  background: rgba(255, 255, 255, 0.2);
  color: var(--text-inverse);
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.notification-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-inverse);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-sm);
  transition: opacity var(--transition-fast);
}

.notification-close:hover {
  opacity: 0.8;
}

/* App Container */
.app {
  min-height: 100vh;
  background: var(--bg-primary);
}

.app-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--space-4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-container {
    padding: var(--space-2);
  }
  
  .pwa-install-prompt {
    left: var(--space-2);
    right: var(--space-2);
  }
  
  .update-notification {
    right: var(--space-2);
    left: var(--space-2);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Focus Styles */
.keyboard-navigation *:focus {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

/* Screen Reader Only */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Skip Link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--bg-primary);
  color: var(--text-primary);
  padding: var(--space-2) var(--space-4);
  text-decoration: none;
  border-radius: var(--radius-md);
  border: 2px solid var(--border-focus);
  z-index: var(--z-tooltip);
  font-weight: var(--font-weight-medium);
  transition: top var(--transition-fast);
}

.skip-link:focus {
  top: 6px;
}

/* Print Styles */
@media print {
  .pwa-install-prompt,
  .offline-indicator,
  .update-notification,
  .loading-screen {
    display: none !important;
  }
  
  .app {
    background: white !important;
    color: black !important;
  }
}
