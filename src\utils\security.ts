import DOMPurify from 'dompurify';

export class SecurityManager {
  private static instance: SecurityManager;
  private cspNonce: string;
  private trustedDomains: Set<string> = new Set([
    'api.constructcalc.com',
    'cdn.jsdelivr.net',
    'fonts.googleapis.com',
    'images.unsplash.com'
  ]);

  constructor() {
    this.cspNonce = this.generateNonce();
    this.initializeCSP();
    this.setupSecurityHeaders();
  }

  static getInstance(): SecurityManager {
    if (!SecurityManager.instance) {
      SecurityManager.instance = new SecurityManager();
    }
    return SecurityManager.instance;
  }

  private generateNonce(): string {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  private initializeCSP(): void {
    const cspPolicy = [
      "default-src 'self'",
      `script-src 'self' 'nonce-${this.cspNonce}' https://cdn.jsdelivr.net`,
      `style-src 'self' 'unsafe-inline' https://fonts.googleapis.com`,
      "img-src 'self' data: https://images.unsplash.com https://via.placeholder.com",
      "font-src 'self' https://fonts.gstatic.com",
      "connect-src 'self' https://api.constructcalc.com https://api.exchangerate-api.com https://ipapi.co",
      "frame-src 'none'",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "upgrade-insecure-requests"
    ].join('; ');

    const meta = document.createElement('meta');
    meta.httpEquiv = 'Content-Security-Policy';
    meta.content = cspPolicy;
    document.head.appendChild(meta);
  }

  private setupSecurityHeaders(): void {
    // Set security-related meta tags
    const securityMetas = [
      { name: 'referrer', content: 'strict-origin-when-cross-origin' },
      { name: 'robots', content: 'noindex, nofollow' }, // For sensitive calculations
      { httpEquiv: 'X-Content-Type-Options', content: 'nosniff' },
      { httpEquiv: 'X-Frame-Options', content: 'DENY' },
      { httpEquiv: 'X-XSS-Protection', content: '1; mode=block' }
    ];

    securityMetas.forEach(meta => {
      const element = document.createElement('meta');
      if (meta.name) element.name = meta.name;
      if (meta.httpEquiv) element.httpEquiv = meta.httpEquiv;
      element.content = meta.content;
      document.head.appendChild(element);
    });
  }

  // Input sanitization
  public sanitizeInput(input: string): string {
    return DOMPurify.sanitize(input, {
      ALLOWED_TAGS: [],
      ALLOWED_ATTR: []
    });
  }

  public sanitizeHTML(html: string): string {
    return DOMPurify.sanitize(html, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'span', 'div', 'p'],
      ALLOWED_ATTR: ['class', 'id']
    });
  }

  // URL validation
  public isValidURL(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return this.trustedDomains.has(urlObj.hostname) || urlObj.protocol === 'data:';
    } catch {
      return false;
    }
  }

  // Data validation
  public validateNumericInput(value: any, min?: number, max?: number): boolean {
    const num = parseFloat(value);
    if (isNaN(num) || !isFinite(num)) return false;
    if (min !== undefined && num < min) return false;
    if (max !== undefined && num > max) return false;
    return true;
  }

  public validateEmail(email: string): boolean {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email) && email.length <= 254;
  }

  // Rate limiting
  private rateLimitMap: Map<string, { count: number; resetTime: number }> = new Map();

  public checkRateLimit(key: string, maxRequests: number = 10, windowMs: number = 60000): boolean {
    const now = Date.now();
    const record = this.rateLimitMap.get(key);

    if (!record || now > record.resetTime) {
      this.rateLimitMap.set(key, { count: 1, resetTime: now + windowMs });
      return true;
    }

    if (record.count >= maxRequests) {
      return false;
    }

    record.count++;
    return true;
  }

  // Session management
  public generateSessionToken(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  public validateSessionToken(token: string): boolean {
    return /^[a-f0-9]{64}$/.test(token);
  }

  // Secure storage
  public secureStore(key: string, value: any): void {
    try {
      const encrypted = this.encrypt(JSON.stringify(value));
      localStorage.setItem(key, encrypted);
    } catch (error) {
      console.error('Failed to store data securely:', error);
    }
  }

  public secureRetrieve(key: string): any {
    try {
      const encrypted = localStorage.getItem(key);
      if (!encrypted) return null;
      
      const decrypted = this.decrypt(encrypted);
      return JSON.parse(decrypted);
    } catch (error) {
      console.error('Failed to retrieve data securely:', error);
      return null;
    }
  }

  private encrypt(text: string): string {
    // Simple XOR encryption for demo purposes
    // In production, use proper encryption libraries
    const key = this.getEncryptionKey();
    let result = '';
    for (let i = 0; i < text.length; i++) {
      result += String.fromCharCode(text.charCodeAt(i) ^ key.charCodeAt(i % key.length));
    }
    return btoa(result);
  }

  private decrypt(encryptedText: string): string {
    const key = this.getEncryptionKey();
    const text = atob(encryptedText);
    let result = '';
    for (let i = 0; i < text.length; i++) {
      result += String.fromCharCode(text.charCodeAt(i) ^ key.charCodeAt(i % key.length));
    }
    return result;
  }

  private getEncryptionKey(): string {
    // Generate a key based on browser fingerprint
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx!.textBaseline = 'top';
    ctx!.font = '14px Arial';
    ctx!.fillText('Security key generation', 2, 2);
    
    return canvas.toDataURL().slice(-16);
  }

  // Error handling
  public logSecurityEvent(event: string, details: any): void {
    const securityLog = {
      timestamp: new Date().toISOString(),
      event,
      details,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // In production, send to security monitoring service
    console.warn('Security Event:', securityLog);
  }

  // Content Security
  public createSecureElement(tagName: string, attributes: Record<string, string> = {}): HTMLElement {
    const element = document.createElement(tagName);
    
    Object.entries(attributes).forEach(([key, value]) => {
      if (this.isValidAttribute(key, value)) {
        element.setAttribute(key, this.sanitizeInput(value));
      }
    });

    return element;
  }

  private isValidAttribute(name: string, value: string): boolean {
    const allowedAttributes = [
      'id', 'class', 'data-*', 'aria-*', 'role', 'tabindex',
      'title', 'alt', 'src', 'href', 'type', 'value', 'placeholder'
    ];

    return allowedAttributes.some(allowed => {
      if (allowed.endsWith('*')) {
        return name.startsWith(allowed.slice(0, -1));
      }
      return name === allowed;
    });
  }

  // API Security
  public createSecureRequest(url: string, options: RequestInit = {}): Request {
    if (!this.isValidURL(url)) {
      throw new Error('Invalid URL');
    }

    const secureOptions: RequestInit = {
      ...options,
      credentials: 'same-origin',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        ...options.headers
      }
    };

    return new Request(url, secureOptions);
  }

  // Privacy protection
  public anonymizeData(data: any): any {
    const anonymized = { ...data };
    
    // Remove or hash sensitive fields
    const sensitiveFields = ['email', 'phone', 'address', 'name'];
    sensitiveFields.forEach(field => {
      if (anonymized[field]) {
        anonymized[field] = this.hashString(anonymized[field]);
      }
    });

    return anonymized;
  }

  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(16);
  }

  // Cleanup
  public cleanup(): void {
    this.rateLimitMap.clear();
  }

  public getNonce(): string {
    return this.cspNonce;
  }
}

// Security utilities
export class SecurityUtils {
  static escapeHTML(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  static unescapeHTML(html: string): string {
    const div = document.createElement('div');
    div.innerHTML = html;
    return div.textContent || div.innerText || '';
  }

  static generateRandomId(): string {
    return 'id_' + Math.random().toString(36).substr(2, 9);
  }

  static isLocalStorageAvailable(): boolean {
    try {
      const test = '__localStorage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  static clearSensitiveData(): void {
    // Clear sensitive data from memory and storage
    const sensitiveKeys = ['user_data', 'calculation_history', 'api_keys'];
    
    sensitiveKeys.forEach(key => {
      localStorage.removeItem(key);
      sessionStorage.removeItem(key);
    });

    // Clear form data
    document.querySelectorAll('input[type="password"], input[type="email"]').forEach(input => {
      (input as HTMLInputElement).value = '';
    });
  }
}

// Initialize security manager
export const securityManager = SecurityManager.getInstance();
