// Dynamic Chart.js loader to avoid build issues
let chartJsLoaded = false;
let chartJsPromise: Promise<any> | null = null;

export async function loadChartJs(): Promise<any> {
  if (chartJsLoaded && (window as any).Chart) {
    return (window as any).Chart;
  }

  if (chartJsPromise) {
    return chartJsPromise;
  }

  chartJsPromise = new Promise(async (resolve, reject) => {
    try {
      // Try to load Chart.js dynamically
      const chartModule = await import('chart.js');
      const Chart = chartModule.Chart;
      const registerables = chartModule.registerables;
      
      // Register all components
      Chart.register(...registerables);
      
      // Make it globally available
      (window as any).Chart = Chart;
      chartJsLoaded = true;
      
      resolve(Chart);
    } catch (error) {
      console.error('Failed to load Chart.js:', error);
      
      // Fallback: try to load from CDN
      try {
        await loadChartJsFromCDN();
        resolve((window as any).Chart);
      } catch (cdnError) {
        console.error('Failed to load Chart.js from CDN:', cdnError);
        reject(cdnError);
      }
    }
  });

  return chartJsPromise;
}

async function loadChartJsFromCDN(): Promise<void> {
  return new Promise((resolve, reject) => {
    if ((window as any).Chart) {
      chartJsLoaded = true;
      resolve();
      return;
    }

    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js';
    script.onload = () => {
      chartJsLoaded = true;
      resolve();
    };
    script.onerror = () => {
      reject(new Error('Failed to load Chart.js from CDN'));
    };
    
    document.head.appendChild(script);
  });
}

// Mock Chart class for development/testing
export class MockChart {
  constructor(ctx: any, config: any) {
    console.log('Mock Chart created:', { ctx, config });
  }

  destroy() {
    console.log('Mock Chart destroyed');
  }

  update() {
    console.log('Mock Chart updated');
  }

  render() {
    console.log('Mock Chart rendered');
  }
}

// Get Chart class (real or mock)
export async function getChart(): Promise<any> {
  try {
    return await loadChartJs();
  } catch (error) {
    console.warn('Using mock Chart due to loading error:', error);
    return MockChart;
  }
}
