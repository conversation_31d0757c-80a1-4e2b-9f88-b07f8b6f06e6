import type { CostBreakdown } from '@/types/calculator';
import { t } from '@/i18n/index';
import { getChart } from '@/utils/chart-loader';

export class CostBreakdownComponent {
  private container: HTMLElement;
  private materialChart: any = null;
  private laborChart: any = null;
  private costData: CostBreakdown | null = null;

  constructor(container: HTMLElement) {
    this.container = container;
    this.initialize();
  }

  private initialize(): void {
    this.render();
  }

  private render(): void {
    this.container.innerHTML = `
      <div class="cost-breakdown-container">
        <!-- Total Cost Display -->
        <div class="total-cost-section">
          <h2 class="section-title">${t('calculator.cost_breakdown.total_estimated_cost')}</h2>
          <div class="total-cost-display" id="totalCostDisplay">
            <div class="cost-range">
              <div class="cost-amount" id="totalAmount">--</div>
              <div class="cost-currency" id="costCurrency">--</div>
            </div>
            <div class="cost-details">
              <div class="cost-per-sqft" id="costPerSqft">-- per sq ft</div>
              <div class="cost-variance">${t('calculator.cost_breakdown.variance_note')}</div>
            </div>
          </div>
        </div>

        <!-- Cost Breakdown Tabs -->
        <div class="breakdown-tabs">
          <button class="tab-btn active" data-tab="materials" aria-selected="true">
            ${t('calculator.cost_breakdown.material_costs')} 🧱
          </button>
          <button class="tab-btn" data-tab="labor" aria-selected="false">
            ${t('calculator.cost_breakdown.labor_costs')} 👷
          </button>
          <button class="tab-btn" data-tab="summary" aria-selected="false">
            Summary 📊
          </button>
        </div>

        <!-- Tab Content -->
        <div class="tab-content">
          <!-- Materials Tab -->
          <div class="tab-panel active" id="materialsTab" role="tabpanel">
            <div class="breakdown-section">
              <div class="breakdown-table-container">
                <table class="breakdown-table" id="materialsTable">
                  <thead>
                    <tr>
                      <th>${t('calculator.labels.material')}</th>
                      <th>${t('calculator.labels.cost_per_sqft')}</th>
                      <th>${t('calculator.labels.total_cost')}</th>
                    </tr>
                  </thead>
                  <tbody id="materialsTableBody">
                    <!-- Dynamic content -->
                  </tbody>
                </table>
              </div>
              <div class="chart-container">
                <canvas id="materialsChart" aria-label="Materials cost breakdown chart"></canvas>
              </div>
              <div class="subtotal">
                <strong>${t('calculator.cost_breakdown.material_costs')}: <span id="materialSubtotal">--</span></strong>
              </div>
            </div>
          </div>

          <!-- Labor Tab -->
          <div class="tab-panel" id="laborTab" role="tabpanel">
            <div class="breakdown-section">
              <div class="breakdown-table-container">
                <table class="breakdown-table" id="laborTable">
                  <thead>
                    <tr>
                      <th>${t('calculator.labels.labor_type')}</th>
                      <th>${t('calculator.labels.skill_level')}</th>
                      <th>${t('calculator.labels.cost_per_sqft')}</th>
                      <th>${t('calculator.labels.total_cost')}</th>
                    </tr>
                  </thead>
                  <tbody id="laborTableBody">
                    <!-- Dynamic content -->
                  </tbody>
                </table>
              </div>
              <div class="chart-container">
                <canvas id="laborChart" aria-label="Labor cost breakdown chart"></canvas>
              </div>
              <div class="subtotal">
                <strong>${t('calculator.cost_breakdown.labor_costs')}: <span id="laborSubtotal">--</span></strong>
              </div>
            </div>
          </div>

          <!-- Summary Tab -->
          <div class="tab-panel" id="summaryTab" role="tabpanel">
            <div class="breakdown-section">
              <div class="summary-grid">
                <div class="summary-item">
                  <div class="summary-label">${t('calculator.cost_breakdown.material_costs')}</div>
                  <div class="summary-value" id="summaryMaterials">--</div>
                  <div class="summary-percentage" id="materialsPercentage">--</div>
                </div>
                <div class="summary-item">
                  <div class="summary-label">${t('calculator.cost_breakdown.labor_costs')}</div>
                  <div class="summary-value" id="summaryLabor">--</div>
                  <div class="summary-percentage" id="laborPercentage">--</div>
                </div>
                <div class="summary-item total">
                  <div class="summary-label">${t('calculator.cost_breakdown.total_estimated_cost')}</div>
                  <div class="summary-value" id="summaryTotal">--</div>
                  <div class="summary-percentage">100%</div>
                </div>
              </div>
              
              <div class="cost-insights">
                <h3>💡 Cost Insights</h3>
                <div class="insights-list" id="costInsights">
                  <!-- Dynamic insights -->
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Export Options -->
        <div class="export-section">
          <h3>${t('calculator.export.title', { defaultValue: 'Export Options' })}</h3>
          <div class="export-buttons">
            <button class="export-btn" id="exportPdfBtn">
              📄 ${t('calculator.export.pdf_report')}
            </button>
            <button class="export-btn" id="exportExcelBtn">
              📊 ${t('calculator.export.excel_breakdown')}
            </button>
            <button class="export-btn" id="shareBtn">
              🔗 ${t('calculator.export.share_link')}
            </button>
          </div>
        </div>
      </div>
    `;

    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    // Tab switching
    this.container.querySelectorAll('.tab-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        const tabName = (btn as HTMLElement).dataset.tab!;
        this.switchTab(tabName);
      });
    });

    // Export buttons
    const exportPdfBtn = this.container.querySelector('#exportPdfBtn') as HTMLButtonElement;
    const exportExcelBtn = this.container.querySelector('#exportExcelBtn') as HTMLButtonElement;
    const shareBtn = this.container.querySelector('#shareBtn') as HTMLButtonElement;

    exportPdfBtn?.addEventListener('click', () => this.exportPDF());
    exportExcelBtn?.addEventListener('click', () => this.exportExcel());
    shareBtn?.addEventListener('click', () => this.shareCalculation());
  }

  private switchTab(tabName: string): void {
    // Update tab buttons
    this.container.querySelectorAll('.tab-btn').forEach(btn => {
      const isActive = (btn as HTMLElement).dataset.tab === tabName;
      btn.classList.toggle('active', isActive);
      btn.setAttribute('aria-selected', isActive.toString());
    });

    // Update tab panels
    this.container.querySelectorAll('.tab-panel').forEach(panel => {
      const isActive = panel.id === `${tabName}Tab`;
      panel.classList.toggle('active', isActive);
    });

    // Initialize charts if needed
    if (tabName === 'materials' && !this.materialChart && this.costData) {
      this.createMaterialChart().catch(console.error);
    } else if (tabName === 'labor' && !this.laborChart && this.costData) {
      this.createLaborChart().catch(console.error);
    }
  }

  public update(costData: CostBreakdown): void {
    this.costData = costData;
    this.updateTotalCost();
    this.updateMaterialsTable();
    this.updateLaborTable();
    this.updateSummary();
    this.updateInsights();
    
    // Recreate charts with new data
    this.destroyCharts();
    if (this.container.querySelector('.tab-btn[data-tab="materials"]')?.classList.contains('active')) {
      this.createMaterialChart().catch(console.error);
    }
    if (this.container.querySelector('.tab-btn[data-tab="labor"]')?.classList.contains('active')) {
      this.createLaborChart().catch(console.error);
    }
  }

  private updateTotalCost(): void {
    if (!this.costData) return;

    const totalAmountEl = this.container.querySelector('#totalAmount') as HTMLElement;
    const costCurrencyEl = this.container.querySelector('#costCurrency') as HTMLElement;
    const costPerSqftEl = this.container.querySelector('#costPerSqft') as HTMLElement;

    if (totalAmountEl) {
      totalAmountEl.textContent = this.formatCurrency(this.costData.total, this.costData.currency);
    }
    
    if (costCurrencyEl) {
      costCurrencyEl.textContent = this.costData.currency;
    }
    
    if (costPerSqftEl) {
      costPerSqftEl.textContent = `${this.formatCurrency(this.costData.pricePerSqft, this.costData.currency)} per sq ft`;
    }
  }

  private updateMaterialsTable(): void {
    if (!this.costData) return;

    const tbody = this.container.querySelector('#materialsTableBody') as HTMLElement;
    const subtotalEl = this.container.querySelector('#materialSubtotal') as HTMLElement;

    tbody.innerHTML = this.costData.materials.items.map(item => `
      <tr>
        <td>
          <span class="item-emoji">${item.emoji}</span>
          <span class="item-name">${item.name}</span>
        </td>
        <td>${this.formatCurrency(item.costPerSqft, this.costData!.currency)}</td>
        <td class="cost-cell">${this.formatCurrency(item.total, this.costData!.currency)}</td>
      </tr>
    `).join('');

    if (subtotalEl) {
      subtotalEl.textContent = this.formatCurrency(this.costData.materials.subtotal, this.costData.currency);
    }
  }

  private updateLaborTable(): void {
    if (!this.costData) return;

    const tbody = this.container.querySelector('#laborTableBody') as HTMLElement;
    const subtotalEl = this.container.querySelector('#laborSubtotal') as HTMLElement;

    tbody.innerHTML = this.costData.labor.items.map(item => `
      <tr>
        <td>
          <span class="item-emoji">${item.emoji}</span>
          <span class="item-name">${item.name}</span>
        </td>
        <td>
          <span class="skill-badge skill-${item.skillLevel}">${item.skillLevel}</span>
        </td>
        <td>${this.formatCurrency(item.costPerSqft, this.costData!.currency)}</td>
        <td class="cost-cell">${this.formatCurrency(item.total, this.costData!.currency)}</td>
      </tr>
    `).join('');

    if (subtotalEl) {
      subtotalEl.textContent = this.formatCurrency(this.costData.labor.subtotal, this.costData.currency);
    }
  }

  private updateSummary(): void {
    if (!this.costData) return;

    const summaryMaterials = this.container.querySelector('#summaryMaterials') as HTMLElement;
    const summaryLabor = this.container.querySelector('#summaryLabor') as HTMLElement;
    const summaryTotal = this.container.querySelector('#summaryTotal') as HTMLElement;
    const materialsPercentage = this.container.querySelector('#materialsPercentage') as HTMLElement;
    const laborPercentage = this.container.querySelector('#laborPercentage') as HTMLElement;

    const materialPercent = (this.costData.materials.subtotal / this.costData.total) * 100;
    const laborPercent = (this.costData.labor.subtotal / this.costData.total) * 100;

    if (summaryMaterials) {
      summaryMaterials.textContent = this.formatCurrency(this.costData.materials.subtotal, this.costData.currency);
    }
    if (summaryLabor) {
      summaryLabor.textContent = this.formatCurrency(this.costData.labor.subtotal, this.costData.currency);
    }
    if (summaryTotal) {
      summaryTotal.textContent = this.formatCurrency(this.costData.total, this.costData.currency);
    }
    if (materialsPercentage) {
      materialsPercentage.textContent = `${materialPercent.toFixed(1)}%`;
    }
    if (laborPercentage) {
      laborPercentage.textContent = `${laborPercent.toFixed(1)}%`;
    }
  }

  private updateInsights(): void {
    if (!this.costData) return;

    const insights: string[] = [];
    const materialPercent = (this.costData.materials.subtotal / this.costData.total) * 100;
    const laborPercent = (this.costData.labor.subtotal / this.costData.total) * 100;

    if (materialPercent > 60) {
      insights.push('🧱 Material costs are higher than average. Consider alternative materials or suppliers.');
    }
    if (laborPercent > 40) {
      insights.push('👷 Labor costs are significant. Consider timing construction during off-peak seasons.');
    }
    if (this.costData.quality === 'luxury') {
      insights.push('✨ Luxury quality adds premium to all components. Ensure budget allocation is adequate.');
    }
    if (this.costData.area > 3000) {
      insights.push('🏠 Large construction projects may benefit from bulk material discounts.');
    }

    const insightsContainer = this.container.querySelector('#costInsights') as HTMLElement;
    insightsContainer.innerHTML = insights.map(insight => `
      <div class="insight-item">${insight}</div>
    `).join('');
  }

  private async createMaterialChart(): Promise<void> {
    const canvas = this.container.querySelector('#materialsChart') as HTMLCanvasElement;
    if (!canvas || !this.costData) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const Chart = await getChart();
    this.materialChart = new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: this.costData.materials.items.map(item => item.name),
        datasets: [{
          data: this.costData.materials.items.map(item => item.total),
          backgroundColor: [
            '#4299e1', '#3182ce', '#2b6cb0', '#2c5282', '#2a4365',
            '#1a365d', '#63b3ed', '#90cdf4', '#bee3f8', '#e6f3ff'
          ],
          borderWidth: 2,
          borderColor: '#ffffff'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true
            }
          },
          tooltip: {
            callbacks: {
              label: (context: any) => {
                const value = context.parsed;
                const total = this.costData!.materials.subtotal;
                const percentage = ((value / total) * 100).toFixed(1);
                return `${context.label}: ${this.formatCurrency(value, this.costData!.currency)} (${percentage}%)`;
              }
            }
          }
        }
      }
    });
  }

  private async createLaborChart(): Promise<void> {
    const canvas = this.container.querySelector('#laborChart') as HTMLCanvasElement;
    if (!canvas || !this.costData) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const Chart = await getChart();
    this.laborChart = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: this.costData.labor.items.map(item => item.name),
        datasets: [{
          label: 'Labor Cost',
          data: this.costData.labor.items.map(item => item.total),
          backgroundColor: '#4299e1',
          borderColor: '#3182ce',
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: (value: any) => this.formatCurrency(value as number, this.costData!.currency)
            }
          }
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: (context: any) => {
                return `${context.label}: ${this.formatCurrency(context.parsed.y, this.costData!.currency)}`;
              }
            }
          }
        }
      }
    });
  }

  private destroyCharts(): void {
    if (this.materialChart) {
      this.materialChart.destroy();
      this.materialChart = null;
    }
    if (this.laborChart) {
      this.laborChart.destroy();
      this.laborChart = null;
    }
  }

  private formatCurrency(amount: number, currency: string): string {
    try {
      return new Intl.NumberFormat(navigator.language, {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(amount);
    } catch {
      const symbols: Record<string, string> = {
        INR: '₹', USD: '$', EUR: '€', GBP: '£', CAD: 'C$', AUD: 'A$', JPY: '¥'
      };
      return `${symbols[currency] || currency} ${amount.toLocaleString()}`;
    }
  }

  private async exportPDF(): Promise<void> {
    // Implementation for PDF export would go here
    console.log('PDF export functionality to be implemented');
  }

  private async exportExcel(): Promise<void> {
    // Implementation for Excel export would go here
    console.log('Excel export functionality to be implemented');
  }

  private async shareCalculation(): Promise<void> {
    if (navigator.share && this.costData) {
      try {
        await navigator.share({
          title: 'Construction Cost Calculation',
          text: `Total estimated cost: ${this.formatCurrency(this.costData.total, this.costData.currency)}`,
          url: window.location.href
        });
      } catch (error) {
        // Fallback to clipboard
        this.copyToClipboard();
      }
    } else {
      this.copyToClipboard();
    }
  }

  private copyToClipboard(): void {
    if (this.costData) {
      const text = `Construction Cost Estimate\nTotal: ${this.formatCurrency(this.costData.total, this.costData.currency)}\nMaterials: ${this.formatCurrency(this.costData.materials.subtotal, this.costData.currency)}\nLabor: ${this.formatCurrency(this.costData.labor.subtotal, this.costData.currency)}`;
      
      navigator.clipboard.writeText(text).then(() => {
        // Show success message
        console.log('Copied to clipboard');
      });
    }
  }

  public destroy(): void {
    this.destroyCharts();
  }
}
