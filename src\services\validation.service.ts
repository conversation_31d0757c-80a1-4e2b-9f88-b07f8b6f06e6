import type { ValidationError, PlotDimensions, Room } from '@/types/calculator';
import DOMPurify from 'dompurify';

export class ValidationService {
  
  validateDimensions(dimensions: PlotDimensions): ValidationError[] {
    const errors: ValidationError[] = [];

    // Validate length
    if (!dimensions.length || dimensions.length <= 0) {
      errors.push({
        field: 'length',
        message: 'Length must be greater than 0',
        code: 'INVALID_LENGTH'
      });
    } else if (dimensions.length > 10000) {
      errors.push({
        field: 'length',
        message: 'Length cannot exceed 10,000 units',
        code: 'LENGTH_TOO_LARGE'
      });
    }

    // Validate width
    if (!dimensions.width || dimensions.width <= 0) {
      errors.push({
        field: 'width',
        message: 'Width must be greater than 0',
        code: 'INVALID_WIDTH'
      });
    } else if (dimensions.width > 10000) {
      errors.push({
        field: 'width',
        message: 'Width cannot exceed 10,000 units',
        code: 'WIDTH_TOO_LARGE'
      });
    }

    // Validate floors
    if (!dimensions.floors || dimensions.floors < 1) {
      errors.push({
        field: 'floors',
        message: 'Must have at least 1 floor',
        code: 'INVALID_FLOORS'
      });
    } else if (dimensions.floors > 10) {
      errors.push({
        field: 'floors',
        message: 'Cannot exceed 10 floors',
        code: 'TOO_MANY_FLOORS'
      });
    }

    // Validate unit
    const validUnits = ['ft', 'yd', 'in', 'm'];
    if (!validUnits.includes(dimensions.unit)) {
      errors.push({
        field: 'unit',
        message: 'Invalid dimension unit',
        code: 'INVALID_UNIT'
      });
    }

    return errors;
  }

  validateRooms(rooms: Room[], dimensions: PlotDimensions): ValidationError[] {
    const errors: ValidationError[] = [];

    if (!rooms || rooms.length === 0) {
      errors.push({
        field: 'rooms',
        message: 'At least one room must be selected',
        code: 'NO_ROOMS_SELECTED'
      });
      return errors;
    }

    // Calculate total area
    const totalRoomArea = rooms.reduce((sum, room) => sum + room.area, 0);
    const plotArea = dimensions.length * dimensions.width * dimensions.floors;

    // Check if total room area exceeds plot area
    if (totalRoomArea > plotArea * 1.2) { // Allow 20% buffer for walls, etc.
      errors.push({
        field: 'rooms',
        message: `Total room area (${totalRoomArea.toLocaleString()}) exceeds available plot space`,
        code: 'AREA_EXCEEDED'
      });
    }

    // Validate individual rooms
    rooms.forEach((room, index) => {
      const roomErrors = this.validateRoom(room, index);
      errors.push(...roomErrors);
    });

    // Check for essential rooms
    const essentialRoomTypes = ['bedroom', 'bathroom', 'kitchen'];
    const selectedRoomTypes = rooms.map(room => room.id.toLowerCase());
    
    essentialRoomTypes.forEach(essentialType => {
      if (!selectedRoomTypes.some(type => type.includes(essentialType))) {
        errors.push({
          field: 'rooms',
          message: `Consider adding a ${essentialType} for basic functionality`,
          code: 'MISSING_ESSENTIAL_ROOM'
        });
      }
    });

    return errors;
  }

  validateRoom(room: Room, index: number): ValidationError[] {
    const errors: ValidationError[] = [];
    const fieldPrefix = `room_${index}`;

    // Validate room ID
    if (!room.id || room.id.trim() === '') {
      errors.push({
        field: `${fieldPrefix}_id`,
        message: 'Room ID is required',
        code: 'MISSING_ROOM_ID'
      });
    }

    // Validate room name
    if (!room.name || room.name.trim() === '') {
      errors.push({
        field: `${fieldPrefix}_name`,
        message: 'Room name is required',
        code: 'MISSING_ROOM_NAME'
      });
    } else {
      // Sanitize room name
      const sanitizedName = DOMPurify.sanitize(room.name);
      if (sanitizedName !== room.name) {
        errors.push({
          field: `${fieldPrefix}_name`,
          message: 'Room name contains invalid characters',
          code: 'INVALID_ROOM_NAME'
        });
      }
    }

    // Validate room area
    if (!room.area || room.area <= 0) {
      errors.push({
        field: `${fieldPrefix}_area`,
        message: 'Room area must be greater than 0',
        code: 'INVALID_ROOM_AREA'
      });
    } else if (room.area > 10000) {
      errors.push({
        field: `${fieldPrefix}_area`,
        message: 'Room area cannot exceed 10,000 sq ft',
        code: 'ROOM_AREA_TOO_LARGE'
      });
    }

    // Validate minimum area constraints
    if (room.minArea && room.area < room.minArea) {
      errors.push({
        field: `${fieldPrefix}_area`,
        message: `${room.name} should be at least ${room.minArea} sq ft`,
        code: 'ROOM_AREA_TOO_SMALL'
      });
    }

    // Validate maximum area constraints
    if (room.maxArea && room.area > room.maxArea) {
      errors.push({
        field: `${fieldPrefix}_area`,
        message: `${room.name} should not exceed ${room.maxArea} sq ft`,
        code: 'ROOM_AREA_TOO_LARGE'
      });
    }

    // Validate category
    const validCategories = ['essential', 'luxury', 'eco', 'specialized', 'cultural'];
    if (!validCategories.includes(room.category)) {
      errors.push({
        field: `${fieldPrefix}_category`,
        message: 'Invalid room category',
        code: 'INVALID_ROOM_CATEGORY'
      });
    }

    return errors;
  }

  validateCurrency(currency: string): ValidationError[] {
    const errors: ValidationError[] = [];
    const validCurrencies = ['INR', 'USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'];

    if (!currency) {
      errors.push({
        field: 'currency',
        message: 'Currency is required',
        code: 'MISSING_CURRENCY'
      });
    } else if (!validCurrencies.includes(currency)) {
      errors.push({
        field: 'currency',
        message: 'Invalid currency code',
        code: 'INVALID_CURRENCY'
      });
    }

    return errors;
  }

  validateCity(city: string): ValidationError[] {
    const errors: ValidationError[] = [];

    if (!city || city.trim() === '') {
      errors.push({
        field: 'city',
        message: 'City is required',
        code: 'MISSING_CITY'
      });
    } else {
      // Sanitize city name
      const sanitizedCity = DOMPurify.sanitize(city);
      if (sanitizedCity !== city) {
        errors.push({
          field: 'city',
          message: 'City name contains invalid characters',
          code: 'INVALID_CITY_NAME'
        });
      }
    }

    return errors;
  }

  validateQuality(quality: string): ValidationError[] {
    const errors: ValidationError[] = [];
    const validQualities = ['standard', 'premium', 'luxury'];

    if (!quality) {
      errors.push({
        field: 'quality',
        message: 'Quality level is required',
        code: 'MISSING_QUALITY'
      });
    } else if (!validQualities.includes(quality)) {
      errors.push({
        field: 'quality',
        message: 'Invalid quality level',
        code: 'INVALID_QUALITY'
      });
    }

    return errors;
  }

  // Utility methods
  static sanitizeInput(input: string): string {
    return DOMPurify.sanitize(input.trim());
  }

  static validateNumericInput(value: any, min?: number, max?: number): boolean {
    const num = parseFloat(value);
    if (isNaN(num)) return false;
    if (min !== undefined && num < min) return false;
    if (max !== undefined && num > max) return false;
    return true;
  }

  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  static validatePhoneNumber(phone: string): boolean {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  }
}
