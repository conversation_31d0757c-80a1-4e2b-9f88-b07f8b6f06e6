{"sections": {"plot_dimensions": "Plot Dimensions", "floors": "Floors", "floor_planning": "Floor Planning", "construction_quality": "Construction Quality", "location": "Location", "currency": "<PERSON><PERSON><PERSON><PERSON>", "results": "Results", "cost_breakdown": "Cost Breakdown", "time_estimate": "Time Estimate", "space_utilization": "Space Utilization"}, "labels": {"length": "Length", "width": "<PERSON><PERSON><PERSON>", "unit": "Unit", "floors": "Number of Floors", "city": "City", "country": "Country", "region": "Region", "quality_level": "Quality Level", "currency": "<PERSON><PERSON><PERSON><PERSON>", "total_area": "Total Area", "usable_area": "Usable Area", "efficiency": "Space Efficiency"}, "placeholders": {"enter_length": "Enter length", "enter_width": "Enter width", "search_rooms": "Search rooms...", "search_cities": "Search cities...", "select_currency": "Select currency"}, "floors": {"ground": "Ground Floor", "first": "First Floor", "second": "Second Floor", "third": "Third Floor", "fourth": "Fourth Floor", "basement": "Basement", "mezzanine": "Mezzanine"}, "quality_descriptions": {"standard": {"title": "Standard Quality", "description": "Basic construction with standard materials and finishes", "features": ["Standard cement and steel", "Basic tiles and fixtures", "Standard electrical fittings", "Basic plumbing"]}, "premium": {"title": "Premium Quality", "description": "Enhanced construction with better materials and finishes", "features": ["High-grade cement and steel", "Premium tiles and fixtures", "Modular electrical fittings", "Premium plumbing fixtures"]}, "luxury": {"title": "Luxury Quality", "description": "High-end construction with premium materials and finishes", "features": ["Premium cement and imported steel", "Designer tiles and luxury fixtures", "Smart electrical systems", "Luxury plumbing and fittings"]}}, "cost_breakdown": {"total_estimated_cost": "Total Estimated Cost", "cost_range": "Cost Range", "material_costs": "Material Costs", "labor_costs": "Labor Costs", "overhead": "Overhead & Profit", "taxes": "Taxes & Permits", "contingency": "Contingency", "price_per_sqft": "Price per sq ft", "variance_note": "±15% variance based on market conditions"}, "time_estimate": {"estimated_duration": "Estimated Construction Duration", "phases": {"planning": "Planning & Permits", "foundation": "Foundation Work", "structure": "Structural Work", "roofing": "Roofing", "electrical": "Electrical Work", "plumbing": "Plumbing", "flooring": "Flooring", "painting": "Painting & Finishing", "final": "Final Inspection"}, "total_time": "Total Construction Time", "working_days": "Working Days", "calendar_days": "Calendar Days"}, "space_utilization": {"room_distribution": "Room Distribution", "area_breakdown": "Area Breakdown", "efficiency_score": "Space Efficiency Score", "recommendations": "Space Optimization Recommendations", "total_rooms": "Total Rooms", "largest_room": "Largest Room", "smallest_room": "Smallest Room"}, "warnings": {"exceeding_plot_area": "⚠️ Room areas exceed available plot space by {{excess}}", "low_efficiency": "⚠️ Space efficiency is below optimal ({{efficiency}}%)", "missing_essential_rooms": "⚠️ Consider adding essential rooms like {{rooms}}", "oversized_rooms": "⚠️ Some rooms may be oversized for the plot", "no_rooms_selected": "⚠️ Please select at least one room"}, "tips": {"drag_to_place": "💡 Drag rooms from the list to place them on the grid", "click_to_remove": "💡 Click on placed rooms to remove them", "right_click_edit": "💡 Right-click on rooms to edit their size", "use_search": "💡 Use the search box to quickly find specific rooms", "quality_affects_cost": "💡 Quality level significantly affects total cost"}, "export": {"pdf_report": "Export PDF Report", "excel_breakdown": "Export Excel Breakdown", "share_link": "Share Calculation Link", "save_project": "Save Project", "load_project": "Load Project"}}