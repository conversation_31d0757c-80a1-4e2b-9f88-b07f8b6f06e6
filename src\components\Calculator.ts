import { CalculatorService } from '../services/calculator.service';
import { FloorPlanComponent } from './FloorPlan';
import { CostBreakdownComponent } from './CostBreakdown';
import { TimeEstimateComponent } from './TimeEstimate';
import { LanguageSwitcher } from './LanguageSwitcher';
import { ThemeToggle } from './ThemeToggle';
import { PerformanceMonitor } from '../utils/performance';
import { I18nHelper, t } from '../i18n/index';
import type { CalculatorConfig, Room, PlotDimensions } from '../types/calculator';

export class Calculator {
  private container: HTMLElement;
  private calculatorService: CalculatorService;
  private floorPlan!: FloorPlanComponent;
  private costBreakdown!: CostBreakdownComponent;
  private timeEstimate!: TimeEstimateComponent;
  private languageSwitcher!: LanguageSwitcher;
  private themeToggle!: ThemeToggle;
  private config: CalculatorConfig;

  constructor(container: HTMLElement, initialConfig: CalculatorConfig) {
    this.container = container;
    this.config = initialConfig;
    this.calculatorService = new CalculatorService(initialConfig);
    
    this.initialize();
  }

  private async initialize(): Promise<void> {
    await this.render();
    this.setupEventListeners();
    this.initializeComponents();
    
    // Initial calculation
    await this.calculate();
  }

  private async render(): Promise<void> {
    this.container.innerHTML = `
      <div class="calculator-container">
        <!-- Header -->
        <header class="calculator-header">
          <div class="header-content">
            <h1 class="app-title">${t('app.title')}</h1>
            <div class="header-controls">
              <div id="languageSwitcher"></div>
              <div id="themeToggle"></div>
            </div>
          </div>
        </header>

        <!-- Main Content -->
        <main class="calculator-main">
          <!-- Input Section -->
          <section class="input-section" aria-label="${t('calculator.sections.input')}">
            <div class="input-grid">
              <!-- Plot Dimensions -->
              <div class="input-group">
                <label class="input-label">${t('calculator.sections.plot_dimensions')}</label>
                <div class="dimension-inputs">
                  <input 
                    type="number" 
                    id="length" 
                    placeholder="${t('calculator.placeholders.enter_length')}"
                    value="${this.config.dimensions.length}"
                    min="0"
                    step="0.1"
                    aria-label="${t('calculator.labels.length')}"
                  >
                  <input 
                    type="number" 
                    id="width" 
                    placeholder="${t('calculator.placeholders.enter_width')}"
                    value="${this.config.dimensions.width}"
                    min="0"
                    step="0.1"
                    aria-label="${t('calculator.labels.width')}"
                  >
                  <select id="dimensionUnit" aria-label="${t('calculator.labels.unit')}">
                    <option value="ft" ${this.config.dimensions.unit === 'ft' ? 'selected' : ''}>Feet</option>
                    <option value="yd" ${this.config.dimensions.unit === 'yd' ? 'selected' : ''}>Yards</option>
                    <option value="in" ${this.config.dimensions.unit === 'in' ? 'selected' : ''}>Inches</option>
                    <option value="m" ${this.config.dimensions.unit === 'm' ? 'selected' : ''}>Meters</option>
                  </select>
                </div>
              </div>

              <!-- Floors -->
              <div class="input-group">
                <label class="input-label" for="floors">${t('calculator.sections.floors')}</label>
                <select id="floors" aria-label="${t('calculator.labels.floors')}">
                  ${[1, 2, 3, 4, 5].map(floor => 
                    `<option value="${floor}" ${this.config.dimensions.floors === floor ? 'selected' : ''}>
                      ${t(`calculator.floors.${floor === 1 ? 'ground' : floor === 2 ? 'first' : floor === 3 ? 'second' : floor === 4 ? 'third' : 'fourth'}`)}
                    </option>`
                  ).join('')}
                </select>
              </div>

              <!-- Quality -->
              <div class="input-group">
                <label class="input-label">${t('calculator.sections.construction_quality')}</label>
                <div class="quality-buttons" role="radiogroup" aria-label="${t('calculator.labels.quality_level')}">
                  ${['standard', 'premium', 'luxury'].map(quality => `
                    <button 
                      class="quality-btn ${this.config.quality === quality ? 'selected' : ''}"
                      data-quality="${quality}"
                      role="radio"
                      aria-checked="${this.config.quality === quality}"
                      aria-label="${t(`calculator.quality_descriptions.${quality}.title`)}"
                    >
                      ${t(`quality.${quality}`)} ${quality === 'standard' ? '🏠' : quality === 'premium' ? '🏡' : '🏰'}
                    </button>
                  `).join('')}
                </div>
              </div>

              <!-- City -->
              <div class="input-group">
                <label class="input-label" for="city">${t('calculator.sections.location')}</label>
                <select id="city" aria-label="${t('calculator.labels.city')}">
                  <option value="delhi" ${this.config.city === 'delhi' ? 'selected' : ''}>Delhi NCR</option>
                  <option value="mumbai" ${this.config.city === 'mumbai' ? 'selected' : ''}>Mumbai</option>
                  <option value="bangalore" ${this.config.city === 'bangalore' ? 'selected' : ''}>Bangalore</option>
                  <option value="hyderabad" ${this.config.city === 'hyderabad' ? 'selected' : ''}>Hyderabad</option>
                  <option value="chandigarh" ${this.config.city === 'chandigarh' ? 'selected' : ''}>Chandigarh</option>
                </select>
              </div>

              <!-- Currency -->
              <div class="input-group">
                <label class="input-label" for="currency">${t('calculator.sections.currency')}</label>
                <select id="currency" aria-label="${t('calculator.labels.currency')}">
                  ${['INR', 'USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'].map(curr => `
                    <option value="${curr}" ${this.config.currency === curr ? 'selected' : ''}>
                      ${t(`currency.${curr}`)}
                    </option>
                  `).join('')}
                </select>
              </div>
            </div>

            <!-- Floor Planning -->
            <div class="floor-planning-section">
              <h2 class="section-title">${t('calculator.sections.floor_planning')}</h2>
              <div id="floorPlan"></div>
            </div>

            <!-- Calculate Button -->
            <button class="calculate-btn" id="calculateBtn" aria-label="${t('actions.calculate')}">
              <span class="btn-text">${t('actions.calculate')}</span>
              <span class="btn-icon">🧮</span>
            </button>
          </section>

          <!-- Results Section -->
          <section class="results-section" aria-label="${t('calculator.sections.results')}">
            <div id="costBreakdown"></div>
            <div id="timeEstimate"></div>
          </section>
        </main>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay" aria-hidden="true">
          <div class="loading-spinner"></div>
          <p class="loading-text">${t('status.calculating')}</p>
        </div>

        <!-- Error Toast -->
        <div class="toast-container" id="toastContainer" aria-live="polite"></div>
      </div>
    `;
  }

  private initializeComponents(): void {
    // Initialize sub-components
    const floorPlanContainer = this.container.querySelector('#floorPlan') as HTMLElement;
    this.floorPlan = new FloorPlanComponent(floorPlanContainer, this.config.rooms);

    const costBreakdownContainer = this.container.querySelector('#costBreakdown') as HTMLElement;
    this.costBreakdown = new CostBreakdownComponent(costBreakdownContainer);

    const timeEstimateContainer = this.container.querySelector('#timeEstimate') as HTMLElement;
    this.timeEstimate = new TimeEstimateComponent(timeEstimateContainer);

    const languageSwitcherContainer = this.container.querySelector('#languageSwitcher') as HTMLElement;
    this.languageSwitcher = new LanguageSwitcher(languageSwitcherContainer);

    const themeToggleContainer = this.container.querySelector('#themeToggle') as HTMLElement;
    this.themeToggle = new ThemeToggle(themeToggleContainer);
  }

  private setupEventListeners(): void {
    // Dimension inputs
    const lengthInput = this.container.querySelector('#length') as HTMLInputElement;
    const widthInput = this.container.querySelector('#width') as HTMLInputElement;
    const floorsSelect = this.container.querySelector('#floors') as HTMLSelectElement;
    const unitSelect = this.container.querySelector('#dimensionUnit') as HTMLSelectElement;

    [lengthInput, widthInput, floorsSelect, unitSelect].forEach(element => {
      element.addEventListener('change', () => this.updateDimensions());
      element.addEventListener('input', () => this.updateDimensions());
    });

    // Quality buttons
    this.container.querySelectorAll('.quality-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const quality = (e.target as HTMLElement).dataset.quality as any;
        this.updateQuality(quality);
      });
    });

    // City and currency
    const citySelect = this.container.querySelector('#city') as HTMLSelectElement;
    const currencySelect = this.container.querySelector('#currency') as HTMLSelectElement;

    citySelect.addEventListener('change', () => this.updateCity());
    currencySelect.addEventListener('change', () => this.updateCurrency());

    // Calculate button
    const calculateBtn = this.container.querySelector('#calculateBtn') as HTMLButtonElement;
    calculateBtn.addEventListener('click', () => this.calculate());

    // Floor plan events
    this.floorPlan.on('roomsChanged', (rooms: Room[]) => {
      this.config.rooms = rooms;
      this.calculatorService.updateRooms(rooms);
      this.calculate();
    });

    // Language change events
    window.addEventListener('languageChanged', () => {
      this.render();
      this.initializeComponents();
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'Enter':
            e.preventDefault();
            this.calculate();
            break;
          case 's':
            e.preventDefault();
            this.saveProject();
            break;
          case 'o':
            e.preventDefault();
            this.loadProject();
            break;
        }
      }
    });
  }

  private updateDimensions(): void {
    const lengthInput = this.container.querySelector('#length') as HTMLInputElement;
    const widthInput = this.container.querySelector('#width') as HTMLInputElement;
    const floorsSelect = this.container.querySelector('#floors') as HTMLSelectElement;
    const unitSelect = this.container.querySelector('#dimensionUnit') as HTMLSelectElement;

    const dimensions: PlotDimensions = {
      length: parseFloat(lengthInput.value) || 0,
      width: parseFloat(widthInput.value) || 0,
      floors: parseInt(floorsSelect.value) || 1,
      unit: unitSelect.value as any
    };

    this.config.dimensions = dimensions;
    this.calculatorService.updateDimensions(dimensions);
    this.calculatorService.calculateDebounced();
  }

  private updateQuality(quality: 'standard' | 'premium' | 'luxury'): void {
    this.config.quality = quality;
    this.calculatorService.updateQuality(quality);
    
    // Update UI
    this.container.querySelectorAll('.quality-btn').forEach(btn => {
      btn.classList.toggle('selected', btn.dataset.quality === quality);
      btn.setAttribute('aria-checked', (btn.dataset.quality === quality).toString());
    });

    this.calculate();
  }

  private updateCity(): void {
    const citySelect = this.container.querySelector('#city') as HTMLSelectElement;
    this.config.city = citySelect.value;
    this.calculatorService.updateCity(citySelect.value);
    this.calculate();
  }

  private updateCurrency(): void {
    const currencySelect = this.container.querySelector('#currency') as HTMLSelectElement;
    this.config.currency = currencySelect.value as any;
    this.calculatorService.updateCurrency(currencySelect.value as any);
    this.calculate();
  }

  private async calculate(): Promise<void> {
    const loadingOverlay = this.container.querySelector('#loadingOverlay') as HTMLElement;
    
    try {
      // Show loading
      loadingOverlay.setAttribute('aria-hidden', 'false');
      loadingOverlay.style.display = 'flex';

      // Perform calculation
      const result = await PerformanceMonitor.measureAsync('calculation', () => 
        this.calculatorService.calculate()
      );

      if (result.isValid && result.costBreakdown && result.timeEstimate) {
        // Update components with results
        this.costBreakdown.update(result.costBreakdown);
        this.timeEstimate.update(result.timeEstimate);

        // Show recommendations if any
        if (result.recommendations && result.recommendations.length > 0) {
          this.showRecommendations(result.recommendations);
        }
      } else {
        // Show errors
        this.showErrors(result.errors);
      }

    } catch (error) {
      console.error('Calculation failed:', error);
      this.showToast(t('errors.calculation_error'), 'error');
    } finally {
      // Hide loading
      loadingOverlay.setAttribute('aria-hidden', 'true');
      loadingOverlay.style.display = 'none';
    }
  }

  private showErrors(errors: any[]): void {
    errors.forEach(error => {
      this.showToast(error.message, 'error');
    });
  }

  private showRecommendations(recommendations: string[]): void {
    recommendations.forEach(recommendation => {
      this.showToast(recommendation, 'info');
    });
  }

  private showToast(message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info'): void {
    const toastContainer = this.container.querySelector('#toastContainer') as HTMLElement;
    
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    toast.setAttribute('role', 'alert');
    
    toastContainer.appendChild(toast);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      toast.remove();
    }, 5000);
  }

  private saveProject(): void {
    const projectData = {
      config: this.config,
      timestamp: Date.now()
    };
    
    localStorage.setItem('constructcalc_project', JSON.stringify(projectData));
    this.showToast(t('messages.data_saved'), 'success');
  }

  private loadProject(): void {
    const saved = localStorage.getItem('constructcalc_project');
    if (saved) {
      try {
        const projectData = JSON.parse(saved);
        this.config = projectData.config;
        this.calculatorService = new CalculatorService(this.config);
        this.render();
        this.initializeComponents();
        this.calculate();
        this.showToast(t('messages.import_success'), 'success');
      } catch (error) {
        this.showToast(t('errors.validation_error'), 'error');
      }
    }
  }

  // Public API
  public getConfig(): CalculatorConfig {
    return { ...this.config };
  }

  public updateConfig(newConfig: Partial<CalculatorConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.calculatorService = new CalculatorService(this.config);
    this.render();
    this.initializeComponents();
  }

  public async exportPDF(): Promise<void> {
    // Implementation for PDF export
    this.showToast('PDF export feature coming soon!', 'info');
  }

  public destroy(): void {
    // Cleanup
    this.floorPlan?.destroy();
    this.costBreakdown?.destroy();
    this.timeEstimate?.destroy();
    this.languageSwitcher?.destroy();
    this.themeToggle?.destroy();
  }
}
