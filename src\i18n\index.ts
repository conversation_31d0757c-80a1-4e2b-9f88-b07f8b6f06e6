import i18next from 'i18next';
import Backend from 'i18next-http-backend';
import LanguageDetector from 'i18next-browser-languagedetector';

// Define supported languages
export const SUPPORTED_LANGUAGES = {
  en: { name: 'English', flag: '🇺🇸' },
  hi: { name: 'हिन्दी', flag: '🇮🇳' },
  es: { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', flag: '🇪🇸' },
  fr: { name: 'Français', flag: '🇫🇷' },
  de: { name: '<PERSON><PERSON><PERSON>', flag: '🇩🇪' },
  zh: { name: '中文', flag: '🇨🇳' },
  ja: { name: '日本語', flag: '🇯🇵' },
  ar: { name: 'العربية', flag: '🇸🇦' },
  pt: { name: 'Portuguê<PERSON>', flag: '🇧🇷' },
  ru: { name: 'Русский', flag: '🇷🇺' }
} as const;

// Initialize i18next
const initI18n = async () => {
  await i18next
    .use(Backend)
    .use(LanguageDetector)
    .init({
      lng: 'en', // Default language
      fallbackLng: 'en',
      debug: process.env.NODE_ENV === 'development',
      
      interpolation: {
        escapeValue: false, // React already escapes values
      },
      
      backend: {
        loadPath: '/locales/{{lng}}/{{ns}}.json',
        addPath: '/locales/{{lng}}/{{ns}}.missing.json'
      },
      
      detection: {
        order: ['localStorage', 'navigator', 'htmlTag'],
        caches: ['localStorage'],
        lookupLocalStorage: 'i18nextLng'
      },
      
      // Resources will be loaded dynamically via backend
      ns: ['common', 'calculator', 'rooms', 'materials'],
      defaultNS: 'common'
    });
};

// Language utilities
export class I18nHelper {
  static formatCurrency(amount: number, currency: string, locale?: string): string {
    const detectedLocale = locale || i18next.language || 'en-US';
    
    try {
      return new Intl.NumberFormat(detectedLocale, {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(amount);
    } catch (error) {
      // Fallback for unsupported currencies
      const symbols: Record<string, string> = {
        INR: '₹',
        USD: '$',
        EUR: '€',
        GBP: '£',
        CAD: 'C$',
        AUD: 'A$',
        JPY: '¥'
      };
      
      return `${symbols[currency] || currency} ${amount.toLocaleString()}`;
    }
  }
  
  static formatNumber(number: number, locale?: string): string {
    const detectedLocale = locale || i18next.language || 'en-US';
    return new Intl.NumberFormat(detectedLocale).format(number);
  }
  
  static formatArea(area: number, unit: string, locale?: string): string {
    const formattedArea = this.formatNumber(area, locale);
    const unitTranslation = i18next.t(`units.${unit}`, { defaultValue: unit });
    return `${formattedArea} ${unitTranslation}`;
  }
  
  static getDirection(): 'ltr' | 'rtl' {
    const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
    return rtlLanguages.includes(i18next.language) ? 'rtl' : 'ltr';
  }
  
  static async changeLanguage(lng: string): Promise<void> {
    await i18next.changeLanguage(lng);
    document.documentElement.lang = lng;
    document.documentElement.dir = this.getDirection();
    
    // Emit custom event for components to react
    window.dispatchEvent(new CustomEvent('languageChanged', { 
      detail: { language: lng } 
    }));
  }
  
  static getCurrentLanguage(): string {
    return i18next.language || 'en';
  }
  
  static getSupportedLanguages() {
    return SUPPORTED_LANGUAGES;
  }
  
  static translateRoomTypes(rooms: any[]): any[] {
    return rooms.map(room => ({
      ...room,
      name: i18next.t(`rooms.${room.key}.name`, { defaultValue: room.name }),
      description: i18next.t(`rooms.${room.key}.description`, { defaultValue: room.description })
    }));
  }
  
  static translateMaterials(materials: any[]): any[] {
    return materials.map(material => ({
      ...material,
      name: i18next.t(`materials.${material.key}.name`, { defaultValue: material.name }),
      description: i18next.t(`materials.${material.key}.description`, { defaultValue: material.description })
    }));
  }
}

// Translation function shorthand
export const t = (key: string, options?: any) => i18next.t(key, options);

// Initialize and export
export { i18next };
export default initI18n;
