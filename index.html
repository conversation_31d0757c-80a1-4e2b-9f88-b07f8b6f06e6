<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <!-- Primary Meta Tags -->
  <title>Advanced Construction Cost Calculator - Professional Estimation Tool</title>
  <meta name="title" content="Advanced Construction Cost Calculator - Professional Estimation Tool" />
  <meta name="description" content="Professional construction cost estimation tool with global support, real-time pricing, interactive floor planning, and multi-currency calculations." />
  <meta name="keywords" content="construction, cost calculator, building estimation, floor plan, architecture, real estate, construction costs" />
  <meta name="author" content="Construction Calculator Team" />
  <meta name="robots" content="index, follow" />
  <meta name="language" content="English" />

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://constructcalc.com/" />
  <meta property="og:title" content="Advanced Construction Cost Calculator" />
  <meta property="og:description" content="Professional construction cost estimation tool with global support and advanced features." />
  <meta property="og:image" content="https://constructcalc.com/images/og-image.png" />
  <meta property="og:image:width" content="1200" />
  <meta property="og:image:height" content="630" />
  <meta property="og:site_name" content="Construction Calculator" />
  <meta property="og:locale" content="en_US" />

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:url" content="https://constructcalc.com/" />
  <meta property="twitter:title" content="Advanced Construction Cost Calculator" />
  <meta property="twitter:description" content="Professional construction cost estimation tool with global support and advanced features." />
  <meta property="twitter:image" content="https://constructcalc.com/images/twitter-image.png" />
  <meta property="twitter:creator" content="@constructcalc" />

  <!-- Favicon and Icons -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico" />
  <link rel="icon" type="image/png" sizes="16x16" href="/icons/icon-16x16.png" />
  <link rel="icon" type="image/png" sizes="32x32" href="/icons/icon-32x32.png" />
  <link rel="apple-touch-icon" sizes="180x180" href="/icons/apple-touch-icon.png" />
  <link rel="mask-icon" href="/icons/safari-pinned-tab.svg" color="#3b82f6" />

  <!-- PWA Manifest -->
  <link rel="manifest" href="/manifest.json" />

  <!-- Theme Colors -->
  <meta name="theme-color" content="#3b82f6" />
  <meta name="msapplication-TileColor" content="#3b82f6" />
  <meta name="msapplication-config" content="/browserconfig.xml" />

  <!-- Security Headers -->
  <meta http-equiv="X-Content-Type-Options" content="nosniff" />
  <meta http-equiv="X-Frame-Options" content="DENY" />
  <meta http-equiv="X-XSS-Protection" content="1; mode=block" />
  <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
  <meta http-equiv="Permissions-Policy" content="camera=(), microphone=(), geolocation=()" />

  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link rel="preconnect" href="https://api.exchangerate-api.com" />
  <link rel="preconnect" href="https://cdn.jsdelivr.net" />

  <!-- DNS Prefetch -->
  <link rel="dns-prefetch" href="//fonts.googleapis.com" />
  <link rel="dns-prefetch" href="//api.constructcalc.com" />
  <link rel="dns-prefetch" href="//images.unsplash.com" />

  <!-- Preload critical resources -->
  <link rel="preload" href="/src/main.ts" as="script" type="module" />
  <link rel="preload" href="/src/styles/main.css" as="style" />

  <!-- Fonts -->
  <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'" />
  <noscript>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" />
  </noscript>
</head>
<body>
  <!-- Skip to main content link for accessibility -->
  <a href="#main-content" class="skip-link">Skip to main content</a>

  <!-- Initial loading screen -->
  <div class="loading-screen" id="initialLoader">
    <div class="loading-container">
      <div class="logo-icon" role="img" aria-label="Construction Calculator">🏗️</div>
      <h1 class="logo-text">Construction Calculator</h1>
      <div class="spinner" role="status" aria-label="Loading"></div>
      <p class="loading-message">Loading application...</p>
    </div>
  </div>

  <!-- Main application container -->
  <div id="root" style="display: none;">
    <main id="main-content" role="main">
      <!-- Application content will be rendered here -->
    </main>
  </div>

  <!-- Fallback for users with JavaScript disabled -->
  <noscript>
    <div style="text-align: center; padding: 2rem; font-family: sans-serif;">
      <h1>🏗️ Construction Cost Calculator</h1>
      <p>This application requires JavaScript to function properly.</p>
      <p>Please enable JavaScript in your browser and refresh the page.</p>
      <p>For the best experience, we recommend using a modern browser like:</p>
      <ul style="display: inline-block; text-align: left;">
        <li>Chrome 90+</li>
        <li>Firefox 88+</li>
        <li>Safari 14+</li>
        <li>Edge 90+</li>
      </ul>
    </div>
  </noscript>

  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Advanced Construction Cost Calculator",
    "description": "Professional construction cost estimation tool with global support, real-time pricing, and interactive floor planning.",
    "url": "https://constructcalc.com",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Any",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "author": {
      "@type": "Organization",
      "name": "Construction Calculator Team"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Construction Calculator",
      "logo": {
        "@type": "ImageObject",
        "url": "https://constructcalc.com/icons/icon-512x512.png"
      }
    },
    "screenshot": [
      "https://constructcalc.com/screenshots/desktop-1.png",
      "https://constructcalc.com/screenshots/mobile-1.png"
    ],
    "featureList": [
      "Interactive Floor Planning",
      "Multi-Currency Support",
      "Real-time Pricing",
      "Global City Support",
      "Accessibility Features",
      "Offline Functionality",
      "Multi-language Support"
    ]
  }
  </script>

  <!-- Critical CSS (inline for performance) -->
  <style>
    /* Critical above-the-fold styles */
    body {
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #ffffff;
      color: #111827;
    }

    .loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
    }

    .loading-container {
      text-align: center;
      max-width: 400px;
      padding: 2rem;
    }

    .logo-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }

    .logo-text {
      font-size: 1.5rem;
      font-weight: 700;
      margin-bottom: 2rem;
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #e5e7eb;
      border-top: 4px solid #3b82f6;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-message {
      color: #6b7280;
      font-size: 0.875rem;
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
      body {
        background: #111827;
        color: #f9fafb;
      }

      .loading-screen {
        background: #111827;
      }

      .spinner {
        border-color: #374151;
        border-top-color: #60a5fa;
      }

      .loading-message {
        color: #9ca3af;
      }
    }

    /* Reduced motion */
    @media (prefers-reduced-motion: reduce) {
      .spinner {
        animation: none;
      }
    }

    .skip-link {
      position: absolute;
      top: -40px;
      left: 6px;
      background: #ffffff;
      color: #111827;
      padding: 0.5rem 1rem;
      text-decoration: none;
      border-radius: 0.375rem;
      border: 2px solid #3b82f6;
      z-index: 10000;
      font-weight: 500;
      transition: top 0.15s ease;
    }

    .skip-link:focus {
      top: 6px;
    }
  </style>
</head>

  <!-- Service Worker Registration -->
  <script>
    // Register service worker for PWA functionality
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', async () => {
        try {
          const registration = await navigator.serviceWorker.register('/sw.js', {
            scope: '/'
          });

          console.log('Service Worker registered successfully:', registration.scope);

          // Listen for updates
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  // New version available
                  console.log('New version available');
                  // Show update notification to user
                  if (window.showUpdateNotification) {
                    window.showUpdateNotification();
                  }
                }
              });
            }
          });
        } catch (error) {
          console.error('Service Worker registration failed:', error);
        }
      });
    }

    // Hide initial loader when app is ready
    window.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        const loader = document.getElementById('initialLoader');
        const root = document.getElementById('root');

        if (loader && root) {
          loader.style.opacity = '0';
          loader.style.transition = 'opacity 0.3s ease';

          setTimeout(() => {
            loader.style.display = 'none';
            root.style.display = 'block';
          }, 300);
        }
      }, 1000); // Minimum loading time for better UX
    });

    // Performance monitoring
    if ('performance' in window && 'PerformanceObserver' in window) {
      // Monitor Core Web Vitals
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'largest-contentful-paint') {
            console.log('LCP:', entry.startTime);
          }
          if (entry.entryType === 'first-input') {
            console.log('FID:', entry.processingStart - entry.startTime);
          }
          if (entry.entryType === 'layout-shift' && !entry.hadRecentInput) {
            console.log('CLS:', entry.value);
          }
        }
      });

      observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
    }

    // Error tracking
    window.addEventListener('error', (e) => {
      console.error('Global error:', e.error);
      // In production, send to error tracking service
    });

    window.addEventListener('unhandledrejection', (e) => {
      console.error('Unhandled promise rejection:', e.reason);
      // In production, send to error tracking service
    });
  </script>

  <!-- Main application script -->
  <script type="module" src="/src/main.ts"></script>
</body>
</html>