{"name": "advanced-construction-calculator", "version": "2.0.0", "description": "Advanced Construction Cost Calculator with global support", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint src/ --ext .js,.ts,.vue", "lint:fix": "eslint src/ --ext .js,.ts,.vue --fix", "format": "prettier --write src/", "type-check": "tsc --noEmit", "analyze": "vite-bundle-analyzer", "pwa:generate": "pwa-asset-generator"}, "dependencies": {"chart.js": "^4.4.0", "chartjs-adapter-date-fns": "^3.0.0", "fabric": "^5.3.0", "three": "^0.158.0", "i18next": "^23.7.6", "i18next-browser-languagedetector": "^7.2.0", "i18next-http-backend": "^2.4.2", "dompurify": "^3.0.5", "lodash-es": "^4.17.21", "date-fns": "^2.30.0", "workbox-window": "^7.0.0", "idb": "^7.1.1"}, "devDependencies": {"vite": "^5.0.0", "vitest": "^1.0.0", "@vitest/ui": "^1.0.0", "eslint": "^8.55.0", "prettier": "^3.1.0", "typescript": "^5.3.0", "@types/three": "^0.158.0", "@types/dompurify": "^3.0.5", "@types/lodash-es": "^4.17.12", "@types/node": "^20.10.4", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "vite-plugin-pwa": "^0.17.0", "vite-bundle-analyzer": "^0.7.0", "pwa-asset-generator": "^6.3.1", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}, "engines": {"node": ">=18.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}