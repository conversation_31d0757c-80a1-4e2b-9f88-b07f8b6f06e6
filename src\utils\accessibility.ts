export class AccessibilityManager {
  private static instance: AccessibilityManager;
  private announcer: HTMLElement;
  private focusHistory: HTMLElement[] = [];
  private keyboardNavigation: boolean = false;

  constructor() {
    this.createScreenReaderAnnouncer();
    this.setupKeyboardNavigation();
    this.setupFocusManagement();
    this.initializeARIA();
  }

  static getInstance(): AccessibilityManager {
    if (!AccessibilityManager.instance) {
      AccessibilityManager.instance = new AccessibilityManager();
    }
    return AccessibilityManager.instance;
  }

  private createScreenReaderAnnouncer(): void {
    this.announcer = document.createElement('div');
    this.announcer.setAttribute('aria-live', 'polite');
    this.announcer.setAttribute('aria-atomic', 'true');
    this.announcer.className = 'sr-only';
    this.announcer.style.cssText = `
      position: absolute !important;
      width: 1px !important;
      height: 1px !important;
      padding: 0 !important;
      margin: -1px !important;
      overflow: hidden !important;
      clip: rect(0, 0, 0, 0) !important;
      white-space: nowrap !important;
      border: 0 !important;
    `;
    document.body.appendChild(this.announcer);
  }

  private setupKeyboardNavigation(): void {
    document.addEventListener('keydown', (e) => {
      // Detect keyboard navigation
      if (e.key === 'Tab') {
        this.keyboardNavigation = true;
        document.body.classList.add('keyboard-navigation');
      }

      // Handle escape key
      if (e.key === 'Escape') {
        this.handleEscape();
      }

      // Handle arrow keys for custom navigation
      if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
        this.handleArrowNavigation(e);
      }
    });

    document.addEventListener('mousedown', () => {
      this.keyboardNavigation = false;
      document.body.classList.remove('keyboard-navigation');
    });
  }

  private setupFocusManagement(): void {
    document.addEventListener('focusin', (e) => {
      const target = e.target as HTMLElement;
      this.focusHistory.push(target);
      
      // Keep only last 10 focused elements
      if (this.focusHistory.length > 10) {
        this.focusHistory.shift();
      }
    });
  }

  private initializeARIA(): void {
    // Set up ARIA landmarks if not present
    this.ensureARIALandmarks();
    
    // Add ARIA labels to interactive elements
    this.enhanceInteractiveElements();
    
    // Set up live regions
    this.setupLiveRegions();
  }

  private ensureARIALandmarks(): void {
    const landmarks = [
      { selector: 'header', role: 'banner' },
      { selector: 'nav', role: 'navigation' },
      { selector: 'main', role: 'main' },
      { selector: 'aside', role: 'complementary' },
      { selector: 'footer', role: 'contentinfo' }
    ];

    landmarks.forEach(({ selector, role }) => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        if (!element.getAttribute('role')) {
          element.setAttribute('role', role);
        }
      });
    });
  }

  private enhanceInteractiveElements(): void {
    // Enhance buttons without proper labels
    document.querySelectorAll('button:not([aria-label]):not([aria-labelledby])').forEach(button => {
      const text = button.textContent?.trim();
      if (!text) {
        const icon = button.querySelector('[class*="icon"], [class*="emoji"]');
        if (icon) {
          button.setAttribute('aria-label', this.getIconDescription(icon.textContent || ''));
        }
      }
    });

    // Enhance form inputs
    document.querySelectorAll('input, select, textarea').forEach(input => {
      this.enhanceFormInput(input as HTMLInputElement);
    });

    // Enhance clickable elements
    document.querySelectorAll('[onclick], .clickable').forEach(element => {
      if (!element.getAttribute('role')) {
        element.setAttribute('role', 'button');
      }
      if (!element.getAttribute('tabindex')) {
        element.setAttribute('tabindex', '0');
      }
    });
  }

  private enhanceFormInput(input: HTMLInputElement): void {
    // Associate with labels
    const label = document.querySelector(`label[for="${input.id}"]`) as HTMLLabelElement;
    if (!label && input.id) {
      const parentLabel = input.closest('label');
      if (parentLabel && !parentLabel.getAttribute('for')) {
        parentLabel.setAttribute('for', input.id);
      }
    }

    // Add required indicators
    if (input.required && !input.getAttribute('aria-required')) {
      input.setAttribute('aria-required', 'true');
    }

    // Add error associations
    const errorElement = document.querySelector(`[id="${input.id}-error"]`);
    if (errorElement) {
      input.setAttribute('aria-describedby', `${input.id}-error`);
    }
  }

  private setupLiveRegions(): void {
    // Create status region for dynamic updates
    const statusRegion = document.createElement('div');
    statusRegion.id = 'status-region';
    statusRegion.setAttribute('aria-live', 'polite');
    statusRegion.setAttribute('aria-atomic', 'false');
    statusRegion.className = 'sr-only';
    statusRegion.style.cssText = this.announcer.style.cssText;
    document.body.appendChild(statusRegion);

    // Create alert region for urgent messages
    const alertRegion = document.createElement('div');
    alertRegion.id = 'alert-region';
    alertRegion.setAttribute('aria-live', 'assertive');
    alertRegion.setAttribute('aria-atomic', 'true');
    alertRegion.className = 'sr-only';
    alertRegion.style.cssText = this.announcer.style.cssText;
    document.body.appendChild(alertRegion);
  }

  private getIconDescription(icon: string): string {
    const iconDescriptions: Record<string, string> = {
      '🏗️': 'Construction',
      '🧮': 'Calculate',
      '🌙': 'Dark mode',
      '☀️': 'Light mode',
      '🔍': 'Search',
      '📏': 'Edit size',
      '🗑️': 'Delete',
      '💾': 'Save',
      '📄': 'Export PDF',
      '📊': 'Export Excel',
      '🔗': 'Share link',
      '⚙️': 'Settings',
      '❌': 'Close',
      '✓': 'Confirm',
      '←': 'Back',
      '→': 'Next'
    };

    return iconDescriptions[icon] || 'Button';
  }

  private handleEscape(): void {
    // Close modals, dropdowns, etc.
    const openModals = document.querySelectorAll('[role="dialog"][aria-hidden="false"]');
    openModals.forEach(modal => {
      this.closeModal(modal as HTMLElement);
    });

    const openDropdowns = document.querySelectorAll('[aria-expanded="true"]');
    openDropdowns.forEach(dropdown => {
      dropdown.setAttribute('aria-expanded', 'false');
    });
  }

  private handleArrowNavigation(e: KeyboardEvent): void {
    const target = e.target as HTMLElement;
    
    // Handle grid navigation
    if (target.classList.contains('grid-cell')) {
      e.preventDefault();
      this.navigateGrid(target, e.key);
      return;
    }

    // Handle menu navigation
    if (target.closest('[role="menu"], [role="menubar"]')) {
      e.preventDefault();
      this.navigateMenu(target, e.key);
      return;
    }

    // Handle tab navigation
    if (target.closest('[role="tablist"]')) {
      e.preventDefault();
      this.navigateTabs(target, e.key);
      return;
    }
  }

  private navigateGrid(cell: HTMLElement, direction: string): void {
    const grid = cell.closest('.floor-plan-grid');
    if (!grid) return;

    const cells = Array.from(grid.querySelectorAll('.grid-cell'));
    const currentIndex = cells.indexOf(cell);
    const gridCols = 10; // Assuming 10 columns

    let nextIndex = currentIndex;
    switch (direction) {
      case 'ArrowUp':
        nextIndex = currentIndex - gridCols;
        break;
      case 'ArrowDown':
        nextIndex = currentIndex + gridCols;
        break;
      case 'ArrowLeft':
        nextIndex = currentIndex - 1;
        break;
      case 'ArrowRight':
        nextIndex = currentIndex + 1;
        break;
    }

    if (nextIndex >= 0 && nextIndex < cells.length) {
      (cells[nextIndex] as HTMLElement).focus();
    }
  }

  private navigateMenu(item: HTMLElement, direction: string): void {
    const menu = item.closest('[role="menu"], [role="menubar"]');
    if (!menu) return;

    const items = Array.from(menu.querySelectorAll('[role="menuitem"]'));
    const currentIndex = items.indexOf(item);

    let nextIndex = currentIndex;
    if (direction === 'ArrowDown' || direction === 'ArrowRight') {
      nextIndex = (currentIndex + 1) % items.length;
    } else if (direction === 'ArrowUp' || direction === 'ArrowLeft') {
      nextIndex = (currentIndex - 1 + items.length) % items.length;
    }

    (items[nextIndex] as HTMLElement).focus();
  }

  private navigateTabs(tab: HTMLElement, direction: string): void {
    const tablist = tab.closest('[role="tablist"]');
    if (!tablist) return;

    const tabs = Array.from(tablist.querySelectorAll('[role="tab"]'));
    const currentIndex = tabs.indexOf(tab);

    let nextIndex = currentIndex;
    if (direction === 'ArrowRight') {
      nextIndex = (currentIndex + 1) % tabs.length;
    } else if (direction === 'ArrowLeft') {
      nextIndex = (currentIndex - 1 + tabs.length) % tabs.length;
    }

    const nextTab = tabs[nextIndex] as HTMLElement;
    nextTab.focus();
    nextTab.click(); // Activate the tab
  }

  // Public methods
  public announce(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
    const region = priority === 'assertive' 
      ? document.getElementById('alert-region') 
      : document.getElementById('status-region');
    
    if (region) {
      region.textContent = message;
      
      // Clear after announcement
      setTimeout(() => {
        region.textContent = '';
      }, 1000);
    }
  }

  public announceCalculationComplete(result: string): void {
    this.announce(`Calculation complete. ${result}`, 'polite');
  }

  public announceError(error: string): void {
    this.announce(`Error: ${error}`, 'assertive');
  }

  public announceRoomAdded(roomName: string): void {
    this.announce(`${roomName} added to floor plan`, 'polite');
  }

  public announceRoomRemoved(roomName: string): void {
    this.announce(`${roomName} removed from floor plan`, 'polite');
  }

  public focusElement(element: HTMLElement): void {
    element.focus();
    
    // Scroll into view if needed
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'nearest',
      inline: 'nearest'
    });
  }

  public trapFocus(container: HTMLElement): void {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
          }
        } else {
          if (document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
          }
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);
    
    // Focus first element
    firstElement?.focus();

    // Return cleanup function
    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  }

  public restoreFocus(): void {
    const lastFocused = this.focusHistory[this.focusHistory.length - 2];
    if (lastFocused && document.contains(lastFocused)) {
      lastFocused.focus();
    }
  }

  public openModal(modal: HTMLElement): void {
    modal.setAttribute('aria-hidden', 'false');
    modal.style.display = 'block';
    
    // Trap focus in modal
    const cleanup = this.trapFocus(modal);
    
    // Store cleanup function for later
    (modal as any)._focusCleanup = cleanup;
  }

  public closeModal(modal: HTMLElement): void {
    modal.setAttribute('aria-hidden', 'true');
    modal.style.display = 'none';
    
    // Cleanup focus trap
    if ((modal as any)._focusCleanup) {
      (modal as any)._focusCleanup();
      delete (modal as any)._focusCleanup;
    }
    
    // Restore focus
    this.restoreFocus();
  }

  public addSkipLink(targetId: string, text: string): void {
    const skipLink = document.createElement('a');
    skipLink.href = `#${targetId}`;
    skipLink.textContent = text;
    skipLink.className = 'skip-link';
    skipLink.style.cssText = `
      position: absolute;
      top: -40px;
      left: 6px;
      background: #000;
      color: #fff;
      padding: 8px;
      text-decoration: none;
      z-index: 1000;
      border-radius: 4px;
    `;
    
    skipLink.addEventListener('focus', () => {
      skipLink.style.top = '6px';
    });
    
    skipLink.addEventListener('blur', () => {
      skipLink.style.top = '-40px';
    });
    
    document.body.insertBefore(skipLink, document.body.firstChild);
  }

  public enhanceColorContrast(): void {
    // Add high contrast mode toggle
    const contrastToggle = document.createElement('button');
    contrastToggle.textContent = 'Toggle High Contrast';
    contrastToggle.className = 'contrast-toggle';
    contrastToggle.setAttribute('aria-label', 'Toggle high contrast mode');
    
    contrastToggle.addEventListener('click', () => {
      document.body.classList.toggle('high-contrast');
      const isHighContrast = document.body.classList.contains('high-contrast');
      this.announce(`High contrast mode ${isHighContrast ? 'enabled' : 'disabled'}`);
    });
    
    // Add to accessibility toolbar
    this.getOrCreateAccessibilityToolbar().appendChild(contrastToggle);
  }

  public addFontSizeControls(): void {
    const toolbar = this.getOrCreateAccessibilityToolbar();
    
    const decreaseBtn = document.createElement('button');
    decreaseBtn.textContent = 'A-';
    decreaseBtn.setAttribute('aria-label', 'Decrease font size');
    decreaseBtn.addEventListener('click', () => this.adjustFontSize(-1));
    
    const increaseBtn = document.createElement('button');
    increaseBtn.textContent = 'A+';
    increaseBtn.setAttribute('aria-label', 'Increase font size');
    increaseBtn.addEventListener('click', () => this.adjustFontSize(1));
    
    toolbar.appendChild(decreaseBtn);
    toolbar.appendChild(increaseBtn);
  }

  private getOrCreateAccessibilityToolbar(): HTMLElement {
    let toolbar = document.getElementById('accessibility-toolbar');
    if (!toolbar) {
      toolbar = document.createElement('div');
      toolbar.id = 'accessibility-toolbar';
      toolbar.className = 'accessibility-toolbar';
      toolbar.style.cssText = `
        position: fixed;
        top: 0;
        right: 0;
        background: #f0f0f0;
        padding: 10px;
        z-index: 1000;
        border: 1px solid #ccc;
      `;
      document.body.appendChild(toolbar);
    }
    return toolbar;
  }

  private adjustFontSize(delta: number): void {
    const currentSize = parseInt(getComputedStyle(document.documentElement).fontSize);
    const newSize = Math.max(12, Math.min(24, currentSize + delta));
    document.documentElement.style.fontSize = `${newSize}px`;
    
    this.announce(`Font size ${delta > 0 ? 'increased' : 'decreased'} to ${newSize} pixels`);
  }

  public cleanup(): void {
    this.focusHistory = [];
    document.body.classList.remove('keyboard-navigation');
  }
}

// Initialize accessibility manager
export const accessibilityManager = AccessibilityManager.getInstance();
