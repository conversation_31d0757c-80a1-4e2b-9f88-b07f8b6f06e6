/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    background: url('https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80') no-repeat center center fixed;
    background-size: cover;
    min-height: 100vh;
    padding: 2rem;
    transition: background 0.3s ease;
}

body.dark-mode {
    background: url('https://images.unsplash.com/photo-1509023464722-18d996393ca8?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80') no-repeat center center fixed;
    background-size: cover;
}

/* Container Styles */
.calculator-container {
    max-width: 1400px;
    width: 100%;
    background: rgba(245, 245, 245, 0.95);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    padding: 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    backdrop-filter: blur(10px);
    transition: background 0.3s ease, box-shadow 0.3s ease;
    margin: 0 auto;
    position: relative;
}

body.dark-mode .calculator-container {
    background: rgba(30, 30, 30, 0.95);
    color: #e0e0e0;
}

/* Input Section Styling */
.input-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    height: auto;
    overflow: visible;
    padding-right: 10px;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

label {
    font-weight: 600;
    color: #333;
    font-size: 1rem;
}

body.dark-mode label {
    color: #e0e0e0;
}

input, select {
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8fafc;
}

body.dark-mode input, body.dark-mode select {
    background: #2d3748;
    border-color: #4a5568;
    color: #e0e0e0;
}

input:focus, select:focus {
    border-color: #4299e1;
    outline: none;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
    background: white;
}

body.dark-mode input:focus, body.dark-mode select:focus {
    background: #4a5568;
}

.quality-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
}

.quality-btn {
    flex: 1;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 10px;
    font-size: 1rem;
    background: #f8fafc;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

body.dark-mode .quality-btn {
    background: #2d3748;
    border-color: #4a5568;
    color: #e0e0e0;
}

.quality-btn:hover {
    background: #4299e1;
    color: white;
    border-color: #4299e1;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(66, 153, 225, 0.2);
}

.quality-btn:active {
    transform: translateY(0);
    box-shadow: none;
}

.quality-btn.selected {
    background: #4299e1;
    color: white;
    border-color: #4299e1;
}

body.dark-mode .quality-btn.selected {
    background: #3182ce;
    border-color: #3182ce;
}

.calculate-btn {
    background: #4299e1;
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
    box-shadow: 0 4px 6px rgba(66, 153, 225, 0.2);
}

.calculate-btn:hover {
    background: #3182ce;
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(66, 153, 225, 0.3);
}

.calculate-btn:active {
    transform: translateY(0);
    box-shadow: 0 4px 6px rgba(66, 153, 225, 0.2);
}

/* Results Section Styling */
.results-section {
    background: rgba(248, 250, 252, 0.9);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: inset 0 4px 6px rgba(0, 0, 0, 0.05), 0 4px 10px rgba(0, 0, 0, 0.2);
    transition: background 0.3s ease;
}

body.dark-mode .results-section {
    background: rgba(45, 55, 72, 0.9);
}

.breakdown-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1rem 0;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    transition: background 0.3s ease;
}

body.dark-mode .breakdown-card {
    background: #2d3748;
}

/* Space Section */
.space-section {
    background: #e3f2fd;
}

body.dark-mode .space-section {
    background: #1e3a5f;
}

/* Material Section */
.material-section {
    background: #f3e5f5;
}

body.dark-mode .material-section {
    background: #4a148c;
}

/* Labor Section */
.labor-section {
    background: #fff3e0;
}

body.dark-mode .labor-section {
    background: #e65100;
}

.space-utilization {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-top: 1rem;
}

.util-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #ddd;
    font-size: 0.95rem;
}

body.dark-mode .util-item {
    border-bottom: 1px solid #4a5568;
}

.progress-bar {
    height: 8px;
    background: #ddd;
    border-radius: 4px;
    overflow: hidden;
    margin: 0.5rem 0;
}

body.dark-mode .progress-bar {
    background: #4a5568;
}

.progress-fill {
    height: 100%;
    background: #4299e1;
    width: 65%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.chart-container {
    margin-top: 1rem;
    width: 100%;
    max-width: 400px;
    height: 300px;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

body.dark-mode th, body.dark-mode td {
    border-bottom: 1px solid #4a5568;
}

th {
    background: #f8fafc;
    font-weight: 600;
}

body.dark-mode th {
    background: #2d3748;
}

tr:hover {
    background: #f8fafc;
}

body.dark-mode tr:hover {
    background: #4a5568;
}

.total-cost {
    font-size: 1.2rem;
    font-weight: 600;
    margin-top: 1rem;
    text-align: center;
    color: white;
    background: #4299e1;
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(66, 153, 225, 0.2);
    transition: all 0.3s ease;
}

.total-cost:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(66, 153, 225, 0.3);
}

.net-carpet-area {
    font-size: 1.2rem;
    font-weight: 600;
    margin-top: 1rem;
    text-align: center;
    color: white;
    background: #48bb78;
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(72, 187, 120, 0.2);
    transition: all 0.3s ease;
}

.net-carpet-area:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(72, 187, 120, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .calculator-container {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .input-section, .results-section {
        padding: 1rem;
    }

    .space-utilization {
        grid-template-columns: 1fr;
    }

    .quality-buttons {
        flex-direction: column;
    }
}

/* Dark Mode Toggle */
.dark-mode-toggle {
    cursor: pointer;
    font-size: 28px;
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(66, 153, 225, 0.9);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.dark-mode-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.4);
}

body.dark-mode .dark-mode-toggle {
    background: rgba(255, 204, 0, 0.9);
    color: #e0e0e0;
}

.emoji {
    display: inline-block;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.dark-mode-toggle .emoji.moon {
    opacity: 1;
    transform: scale(1);
}

.dark-mode-toggle .emoji.sun {
    opacity: 0;
    transform: scale(0);
    position: absolute;
}

body.dark-mode .dark-mode-toggle .emoji.moon {
    opacity: 0;
    transform: scale(0);
}

body.dark-mode .dark-mode-toggle .emoji.sun {
    opacity: 1;
    transform: scale(1);
}

/* Floor Planning Section */
.floor-planning-box {
    display: flex;
    gap: 2rem;
    margin-top: 1rem;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.room-options-box, .floor-plan-grid-box {
    flex: 1;
    background: rgba(248, 250, 252, 0.9);
    border-radius: 12px;
    padding: 1rem;
    box-shadow: inset 0 4px 6px rgba(0, 0, 0, 0.05);
}

.room-options-box h3, .floor-plan-grid-box h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
}

.room-options {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
}


.room-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.room-option:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.5);
}

body.dark-mode .room-option {
    background: rgba(255, 255, 255, 0.05);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

body.dark-mode .room-option:hover {
    border-color: rgba(66, 153, 225, 0.5);
}

.room-option.selected {
    border-color: #008cff;
    background: #4299e1;
    color: white;
    animation: selectPop 0.4s ease;
}

@keyframes selectPop {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.room-option.search-match {
    animation: highlightPulse 1.5s ease;
    border: 2px solid #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
}

@keyframes highlightPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.room-option span {
    flex-grow: 1;
    text-align: left;
    font-weight: 500;
    letter-spacing: 0.02em;
}

/* Grid Container Styling */
.floor-plan-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(10, 1fr);
    gap: 7px;
    width: 100%;
    height: 300px;
    border: 2px solid #ddd;
    border-radius: 15px;
    padding: 10px;
    background: rgba(248, 250, 252, 0.9);
    box-shadow: inset 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    overflow-x: hidden;
}

/* Grid Cell Styling */
.grid-cell {
    border: 1px solid #ddd;
    border-radius: 15px;
    background: linear-gradient(145deg, #ffffff, #f0f0f0);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    min-width: 60px;
    min-height: 60px;
    overflow: hidden;
}

.grid-cell:hover {
    background: linear-gradient(145deg, #4299e1, #3182ce);
    color: white;
    border-color: #4299e1;
    transform: scale(1.05);
    box-shadow: 0 6px 8px rgba(66, 153, 225, 0.3);
}

.grid-cell.occupied {
    background: linear-gradient(145deg, #4299e1, #3182ce);
    color: white;
    border-color: #4299e1;
    animation: pop 0.3s ease;
}

.grid-cell.occupied:hover {
    background: linear-gradient(145deg, #3182ce, #2b6cb0);
    border-color: #3182ce;
}

@keyframes pop {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Dark Mode Grid Styling */
body.dark-mode .floor-plan-grid {
    background: rgba(45, 55, 72, 0.9);
    border-color: #4a5568;
}

body.dark-mode .grid-cell {
    background: linear-gradient(145deg, #4a5568, #2d3748);
    border-color: #2d3748;
    color: #e0e0e0;
}

body.dark-mode .grid-cell:hover {
    background: linear-gradient(145deg, #3182ce, #2b6cb0);
    color: white;
}

body.dark-mode .grid-cell.occupied {
    background: linear-gradient(145deg, #3182ce, #2b6cb0);
    border-color: #3182ce;
}

body.dark-mode .grid-cell.occupied:hover {
    background: linear-gradient(145deg, #2b6cb0, #1e4a87);
}

/* Scrollbar Styling */
.floor-plan-grid::-webkit-scrollbar {
    width: 8px;
}

.floor-plan-grid::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.floor-plan-grid::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
}

.floor-plan-grid::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Time Estimate Section */
.time-section {
    background: #fff3e0;
}

body.dark-mode .time-section {
    background: #e65100;
}

.time-estimate {
    font-size: 1.2rem;
    font-weight: 600;
    margin-top: 1rem;
    text-align: center;
    color: white;
    background: #ff6f61;
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(255, 111, 97, 0.2);
    transition: all 0.3s ease;
}

.time-estimate:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(255, 111, 97, 0.3);
}

.warning {
    padding: 0.8rem;
    background: #f8c2c2;
    border-radius: 8px;
    border: 2px solid #ff4444;
    margin-top: 1rem;
    animation: shake 0.5s ease;
    display: none;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

body.dark-mode .warning {
    background: #2d1a1a;
    border-color: #ff6666;
}

.util-item span:first-child {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.util-item span:first-child::before {
    content: attr(data-count);
    background: #4299e1;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
}

.net-carpet-area {
    font-size: 1.1rem;
    padding: 1.2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.area-percentage {
    font-size: 0.9em;
    opacity: 0.9;
    font-weight: 400;
}

.util-item.total-row {
    font-weight: 600;
    background: rgba(66, 153, 225, 0.1);
    padding: 0.8rem;
    border-radius: 8px;
    margin-top: 0.5rem;
}

body.dark-mode .util-item.total-row {
    background: rgba(66, 153, 225, 0.2);
}

.total-cost-section {
    background: #e8eaf6 !important;
    order: -1;
}

body.dark-mode .total-cost-section {
    background: #1a237e !important;
}

.total-estimate {
    text-align: center;
    margin: 1.5rem 0;
}

.cost-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 2.5rem;
    font-weight: 700;
    color: #3f51b5;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

body.dark-mode .cost-display {
    background: rgba(0, 0, 0, 0.2);
    color: #7986cb;
}

.currency-symbol {
    font-size: 1.5rem;
    margin-right: 0.5rem;
}

.cost-amount {
    letter-spacing: 2px;
}

.cost-range-note {
    font-size: 0.9rem;
    color: #666;
    margin-top: 0.5rem;
}

body.dark-mode .cost-range-note {
    color: #b0b0b0;
}

.total-cost-section:hover .cost-display {
    transform: scale(1.05);
    box-shadow: 0 6px 8px rgba(63, 81, 181, 0.3);
}

.total-cost-section {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%) !important;
    position: relative;
    overflow: hidden;
    order: -1;
}

.total-cost-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, 
        rgba(66, 153, 225, 0.1) 25%,
        transparent 25%,
        transparent 50%,
        rgba(66, 153, 225, 0.1) 50%,
        rgba(66, 153, 225, 0.1) 75%,
        transparent 75%,
        transparent
    );
    background-size: 4px 4px;
    animation: patternSlide 20s linear infinite;
    opacity: 0.3;
}

@keyframes patternSlide {
    0% { transform: translate(0,0); }
    100% { transform: translate(100px,100px); }
}

.cost-range-display {
    position: relative;
    padding: 2rem 1rem;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin: 1.5rem 0;
    transition: all 0.3s ease;
}

.range-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 4px;
    background: linear-gradient(90deg, #4299e1 0%, #9c27b0 100%);
    width: 100%;
    opacity: 0.3;
}

.cost-figures {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
}

.cost-figure {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 12px;
    background: rgba(66, 153, 225, 0.1);
    position: relative;
}

.cost-figure .currency {
    font-size: 1.5rem;
    color: #2b6cb0;
}

.cost-figure .amount {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2b6cb0;
    letter-spacing: -1px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.separator {
    font-size: 2rem;
    color: #4a5568;
    margin-top: -1rem;
    opacity: 0.7;
}

.range-note {
    text-align: center;
    font-size: 0.9rem;
    color: #4a5568;
    position: relative;
}

.sparkles {
    position: absolute;
    top: -2rem;
    right: 1rem;
    font-size: 1.5rem;
    opacity: 0.3;
    animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.6; transform: scale(1.2); }
}

/* Dark Mode Adjustments */
body.dark-mode .total-cost-section {
    background: linear-gradient(135deg, #1a237e 0%, #4a148c 100%) !important;
}

body.dark-mode .cost-range-display {
    background: rgba(0, 0, 0, 0.3);
}

body.dark-mode .cost-figure {
    background: rgba(255, 255, 255, 0.1);
}

body.dark-mode .cost-figure .currency,
body.dark-mode .cost-figure .amount {
    color: #90cdf4;
}

body.dark-mode .separator {
    color: #cbd5e0;
}

body.dark-mode .range-note {
    color: #cbd5e0;
}

/* Hover Effects */
.total-cost-section:hover .cost-range-display {
    transform: translateY(-3px);
    box-shadow: 0 8px 12px rgba(66, 153, 225, 0.2);
}

.total-cost-section:hover .range-bar {
    opacity: 0.6;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .cost-figures {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .separator {
        transform: rotate(90deg);
        margin: -0.5rem 0;
    }
    
    .cost-figure .amount {
        font-size: 2rem;
    }
}

.dimension-inputs {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.dimension-inputs input {
    flex: 1;
    min-width: 120px;
}

.dimension-inputs select {
    flex: 0 0 120px;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 10px;
    font-size: 1rem;
    background: #f8fafc;
    transition: all 0.3s ease;
}

body.dark-mode .dimension-inputs select {
    background: #2d3748;
    border-color: #4a5568;
    color: #e0e0e0;
}

/* Room Search and Options */
.room-search {
    width: 100%; 
    position: relative;
}

#roomSearch {
    box-sizing: border-box; 
    width: 100%;
    padding: 12px 20px; 
    font-size: 0.95rem; 
    white-space: nowrap;
    overflow-x: auto;
    text-overflow: clip;
}

.room-categories {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.category h4 {
    margin: 0 0 1rem 0;
    color: #4a5568;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

body.dark-mode .category h4 {
    color: #cbd5e0;
}

.room-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 0.75rem;
}


.room-option span {
    flex-grow: 1;
    text-align: left;
}

.room-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.room-option:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.5);
}

body.dark-mode .room-option.eco {
    background: rgba(72, 187, 120, 0.1);
}

/* Keyboard navigation */
.room-option:focus-visible {
    outline: 2px solid #4299e1;
    outline-offset: 2px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .room-options {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .room-option {
        font-size: 0.85rem;
    }
}

/* Add visual feedback for selection */
.room-option.selected {
    border-color: #008cff;
    background: #4299e1;
    color: white;
    animation: selectPop 0.4s ease;
}

@keyframes selectPop {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Add scrollable container for room options */
.room-options-box {
    max-height: 380px;
    overflow-y: auto;
    padding-right: 8px;
}

/* Make room buttons more compact */
.room-options {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.5rem;
}

/* Custom scrollbar for room options */
.room-options-box::-webkit-scrollbar {
    width: 6px;
}

.room-options-box::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.1);
    border-radius: 4px;
}

.room-options-box::-webkit-scrollbar-thumb {
    background: #4299e1;
    border-radius: 4px;
}

body.dark-mode .room-options-box::-webkit-scrollbar-thumb {
    background: #3182ce;
}

/* Enhanced Room Options */
.room-option {
    padding: 12px;
    border-radius: 12px;
    font-size: 0.95rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 0.8rem;
    background: rgba(209, 19, 19, 0.9);
    border: 2px solid transparent;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    min-height: 60px;
}

/* Category Color Coding */
.room-option.essential {
    border-left: 4px solid #4299e1;
    background: rgba(66, 153, 225, 0.08);
}

.room-option.luxury {
    border-left: 4px solid #9c27b0;
    background: rgba(156, 39, 176, 0.08);
}

.room-option.eco {
    border-left: 4px solid #48bb78;
    background: rgba(72, 187, 120, 0.08);
}

.room-option.specialized {
    border-left: 4px solid #f59e0b;
    background: rgba(245, 158, 11, 0.08);
}

.room-option.cultural {
    border-left: 4px solid #e53e3e;
    background: rgba(229, 62, 62, 0.08);
}

/* Selected State */
.room-option.selected {
    background: #2f95e8 !important;
    color: white;
    border-color: #007aed !important;
    box-shadow: 0 10px 22px rgba(2, 19, 33, 0.3);
    animation: selectPop 0.3s ease;
}

.room-option.selected::after {
    content: '✓';
    position: absolute;
    top: 4px;
    right: 8px;
    font-size: 1em;
    color: rgba(0, 0, 0, 0.8);
}

/* Search Highlight */
.room-option.search-match {
    animation: highlightPulse 1.5s ease;
    border: 2px solid #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
}

@keyframes highlightPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

/* Improved Typography */
.room-option span {
    flex-grow: 1;
    text-align: left;
    font-weight: 500;
    letter-spacing: 0.02em;
}

/* Dark Mode Adjustments */
body.dark-mode .room-option {
    background: rgba(255, 255, 255, 0.05);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

body.dark-mode .room-option:hover {
    border-color: rgba(66, 153, 225, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
    .room-options {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    }
    
    .room-option {
        font-size: 0.85rem;
        min-height: 50px;
        padding: 10px;
    }
}

.room-option {
    transition: all 0.2s ease;
    z-index: 1;
    position: relative;
}

.room-option:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    z-index: 2;
}

.room-option.selected {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
}

.room-option:active {
    transform: scale(0.98) !important;
}

.room-option.selected:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 15px rgba(66, 153, 225, 0.4);
}

@keyframes searchHighlight {
    0% { background-color: transparent; }
    50% { background-color: rgba(66, 153, 225, 0.3); }
    100% { background-color: transparent; }
}

.room-option.search-match {
    animation: searchHighlight 1.5s ease;
}

.tooltip {
    font-size: 0.8em;
    color: #666;
}

.grid-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 0.9rem;
    pointer-events: none;
    z-index: 1000;
    transform: translate(10px, 10px);
    transition: opacity 0.2s;
    opacity: 0;
}

body.dark-mode .grid-tooltip {
    background: rgba(255, 255, 255, 0.9);
    color: #2d3748;
}

body.dark-mode .room-option {
    background: rgba(255, 255, 255, 0.05) !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
}

body.dark-mode .room-option.selected {
    background: #2b6cb0 !important;
    border-color: #4299e1 !important;
}

.edit-room {
    background: none;
    border: none;
    padding: 4px;
    cursor: pointer;
    opacity: 0.7;
    transition: all 0.2s ease;
}

.edit-room:hover {
    opacity: 1;
    transform: scale(1.1);
}

.room-option:hover .edit-room {
    display: inline-block;
}

.edit-room {
    background: none;
    border: none;
    padding: 2px;
    cursor: pointer;
    opacity: 0.5;
    transition: all 0.2s ease;
    margin-left: auto;
    font-size: 0.8em;
    position: relative;
    z-index: 3;
}

.edit-room:hover {
    opacity: 1;
    transform: scale(1.2);
    background: rgba(94, 89, 89, 0.2);
    border-radius: 4px;
}

/* Toast Notifications */
.toast {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 0.9rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from { transform: translate(-50%, 100%); }
    to { transform: translate(-50%, 0); }
}

body.dark-mode .toast {
    background: rgba(255, 255, 255, 0.9);
    color: #2d3748;
}

/* Prevent edit button from affecting room button layout */
.room-option {
    position: relative;
    padding-right: 30px !important;
}

.grid-cell[data-room-type] {
    position: relative;
    padding: 5px;
}

.grid-cell::after {
    content: attr(data-room-type);
    position: absolute;
    bottom: 2px;
    right: 2px;
    font-size: 0.6em;
    opacity: 0.7;
}

/* Custom room styling */
.room-option[style*="border: 2px solid #48bb78"] {
    animation: pulseCustom 1.5s infinite;
}

@keyframes pulseCustom {
    0% { box-shadow: 0 0 0 0 rgba(72, 187, 120, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(72, 187, 120, 0); }
    100% { box-shadow: 0 0 0 0 rgba(72, 187, 120, 0); }
}

.grid-cell[data-room-type] {
    font-size: 0.7em;
    word-break: break-word;
    padding: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.edit-room {
    position: static;
    margin-top: 4px;
    padding: 2px 4px;
    font-size: 0.8em;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
}

.grid-cell.occupied {
    background: linear-gradient(145deg, #4299e1dd, #3182cedd);
}