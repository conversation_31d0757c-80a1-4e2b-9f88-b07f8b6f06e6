import type { Room, FloorPlanCell } from '@/types/calculator';
import { t } from '@/i18n/index.js';
import { debounce } from '@/utils/performance';

export class FloorPlanComponent {
  private container: HTMLElement;
  private rooms: Room[];
  private grid: FloorPlanCell[][] = [];
  private selectedRoom: Room | null = null;
  private gridSize = { rows: 10, cols: 10 };
  private eventListeners: Map<string, Function[]> = new Map();

  // Room categories and data
  private roomCategories = {
    essential: [
      { id: 'bedroom', name: '🛏️ Bedroom', area: 200, emoji: '🛏️' },
      { id: 'bathroom', name: '🚿 Bathroom', area: 100, emoji: '🚿' },
      { id: 'kitchen', name: '🍳 Kitchen', area: 150, emoji: '🍳' },
      { id: 'living-room', name: '🛋️ Living Room', area: 250, emoji: '🛋️' },
      { id: 'garage', name: '🚗 Garage', area: 300, emoji: '🚗' },
      { id: 'balcony', name: '🌿 Balcony', area: 50, emoji: '🌿' },
      { id: 'storage', name: '🗄️ Storage', area: 50, emoji: '🗄️' },
      { id: 'dining-area', name: '🍽️ Dining Area', area: 150, emoji: '🍽️' }
    ],
    luxury: [
      { id: 'study-room', name: '📚 Study Room', area: 100, emoji: '📚' },
      { id: 'game-room', name: '🎮 Game Room', area: 200, emoji: '🎮' },
      { id: 'meditation-room', name: '🧘 Meditation Room', area: 100, emoji: '🧘' },
      { id: 'gym', name: '🏋️ Gym', area: 150, emoji: '🏋️' },
      { id: 'home-theater', name: '🎥 Home Theater', area: 300, emoji: '🎥' },
      { id: 'wine-cellar', name: '🍷 Wine Cellar', area: 150, emoji: '🍷' },
      { id: 'library', name: '📖 Library', area: 180, emoji: '📖' },
      { id: 'indoor-pool', name: '🏊 Indoor Pool', area: 400, emoji: '🏊' }
    ],
    eco: [
      { id: 'solar-panels', name: '☀️ Solar Panels', area: 100, emoji: '☀️' },
      { id: 'rainwater-harvesting', name: '🌧️ Rainwater Harvesting', area: 50, emoji: '🌧️' },
      { id: 'energy-efficient-appliances', name: '⚡ Energy-Efficient Appliances', area: 100, emoji: '⚡' },
      { id: 'geothermal-heating', name: '🌍 Geothermal Heating', area: 200, emoji: '🌍' },
      { id: 'green-roof', name: '🌱 Green Roof', area: 250, emoji: '🌱' },
      { id: 'wind-turbine', name: '🌪️ Wind Turbine', area: 150, emoji: '🌪️' }
    ],
    specialized: [
      { id: 'panic-room', name: '🚨 Panic Room', area: 120, emoji: '🚨' },
      { id: 'recording-studio', name: '🎙️ Recording Studio', area: 300, emoji: '🎙️' },
      { id: 'darkroom', name: '🎞️ Darkroom', area: 100, emoji: '🎞️' },
      { id: 'sauna', name: '🧖 Sauna', area: 80, emoji: '🧖' }
    ],
    cultural: [
      { id: 'chapel', name: '⛪ Chapel', area: 200, emoji: '⛪' },
      { id: 'tea-ceremony-room', name: '🍵 Tea Ceremony Room', area: 150, emoji: '🍵' },
      { id: 'yurt', name: '⛺ Yurt', area: 300, emoji: '⛺' }
    ]
  };

  constructor(container: HTMLElement, initialRooms: Room[] = []) {
    this.container = container;
    this.rooms = initialRooms;
    this.initialize();
  }

  private initialize(): void {
    this.initializeGrid();
    this.render();
    this.setupEventListeners();
  }

  private initializeGrid(): void {
    this.grid = [];
    for (let row = 0; row < this.gridSize.rows; row++) {
      this.grid[row] = [];
      for (let col = 0; col < this.gridSize.cols; col++) {
        this.grid[row][col] = {
          id: `cell-${row}-${col}`,
          x: col,
          y: row,
          isOccupied: false
        };
      }
    }
  }

  private render(): void {
    this.container.innerHTML = `
      <div class="floor-plan-container">
        <!-- Room Selection Panel -->
        <div class="room-selection-panel">
          <div class="room-search">
            <input 
              type="text" 
              id="roomSearch" 
              placeholder="${t('calculator.placeholders.search_rooms')}"
              aria-label="${t('actions.search')}"
            >
          </div>
          
          <div class="room-categories">
            ${Object.entries(this.roomCategories).map(([category, rooms]) => `
              <div class="room-category">
                <h3 class="category-title">${t(`rooms.categories.${category}`)}</h3>
                <div class="room-options" data-category="${category}">
                  ${rooms.map(room => `
                    <button 
                      class="room-option ${category}" 
                      data-room-id="${room.id}"
                      data-room-area="${room.area}"
                      title="${t(`rooms.${room.id}.description`, { defaultValue: room.name })}"
                      aria-label="${room.name} - ${room.area} sq ft"
                    >
                      <span class="room-emoji">${room.emoji}</span>
                      <span class="room-name">${t(`rooms.${room.id}.name`, { defaultValue: room.name })}</span>
                      <span class="room-area">${room.area} sq ft</span>
                      <button class="edit-room-btn" data-room-id="${room.id}" aria-label="Edit ${room.name} size">📏</button>
                    </button>
                  `).join('')}
                </div>
              </div>
            `).join('')}
          </div>
        </div>

        <!-- Floor Plan Grid -->
        <div class="floor-plan-grid-container">
          <div class="grid-header">
            <h3>${t('calculator.sections.floor_planning')}</h3>
            <div class="grid-controls">
              <button class="clear-grid-btn" aria-label="Clear all rooms">🗑️ Clear All</button>
              <button class="auto-arrange-btn" aria-label="Auto arrange rooms">🎯 Auto Arrange</button>
            </div>
          </div>
          
          <div class="floor-plan-grid" id="floorPlanGrid">
            ${this.renderGrid()}
          </div>
          
          <div class="grid-info">
            <div class="space-utilization">
              <div class="utilization-item">
                <span class="label">Total Rooms:</span>
                <span class="value" id="totalRooms">0</span>
              </div>
              <div class="utilization-item">
                <span class="label">Total Area:</span>
                <span class="value" id="totalArea">0 sq ft</span>
              </div>
              <div class="utilization-item">
                <span class="label">Space Efficiency:</span>
                <span class="value" id="spaceEfficiency">0%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private renderGrid(): string {
    return this.grid.map((row, rowIndex) => 
      row.map((cell, colIndex) => `
        <div 
          class="grid-cell ${cell.isOccupied ? 'occupied' : ''}"
          data-row="${rowIndex}"
          data-col="${colIndex}"
          data-cell-id="${cell.id}"
          role="button"
          tabindex="0"
          aria-label="Grid cell ${rowIndex + 1}, ${colIndex + 1}${cell.room ? ` - ${cell.room.name}` : ''}"
        >
          ${cell.room ? `
            <span class="cell-emoji">${cell.room.emoji}</span>
            <span class="cell-name">${cell.room.name.split(' ').slice(-1)[0]}</span>
          ` : ''}
        </div>
      `).join('')
    ).join('');
  }

  private setupEventListeners(): void {
    // Room selection
    this.container.querySelectorAll('.room-option').forEach(button => {
      button.addEventListener('click', (e) => {
        e.stopPropagation();
        if ((e.target as HTMLElement).classList.contains('edit-room-btn')) {
          return; // Let edit button handle its own event
        }
        this.selectRoom(button as HTMLElement);
      });
    });

    // Room editing
    this.container.querySelectorAll('.edit-room-btn').forEach(button => {
      button.addEventListener('click', (e) => {
        e.stopPropagation();
        const roomId = (button as HTMLElement).dataset.roomId!;
        this.editRoomSize(roomId);
      });
    });

    // Grid cell interactions
    this.container.querySelectorAll('.grid-cell').forEach(cell => {
      cell.addEventListener('click', () => this.handleCellClick(cell as HTMLElement));
      cell.addEventListener('keydown', (e: Event) => {
        const keyEvent = e as KeyboardEvent;
        if (keyEvent.key === 'Enter' || keyEvent.key === ' ') {
          e.preventDefault();
          this.handleCellClick(cell as HTMLElement);
        }
      });
    });

    // Search functionality
    const searchInput = this.container.querySelector('#roomSearch') as HTMLInputElement;
    searchInput.addEventListener('input', debounce(() => {
      this.filterRooms(searchInput.value);
    }, 300));

    // Grid controls
    const clearBtn = this.container.querySelector('.clear-grid-btn') as HTMLButtonElement;
    clearBtn.addEventListener('click', () => this.clearGrid());

    const autoArrangeBtn = this.container.querySelector('.auto-arrange-btn') as HTMLButtonElement;
    autoArrangeBtn.addEventListener('click', () => this.autoArrangeRooms());

    // Drag and drop (if supported)
    if ('draggable' in document.createElement('div')) {
      this.setupDragAndDrop();
    }
  }

  private selectRoom(button: HTMLElement): void {
    // Remove previous selection
    this.container.querySelectorAll('.room-option').forEach(btn => 
      btn.classList.remove('selected')
    );

    // Select new room
    button.classList.add('selected');
    
    const roomId = button.dataset.roomId!;
    const roomArea = parseInt(button.dataset.roomArea!);
    const category = (button.closest('.room-options') as HTMLElement)?.dataset.category as any;
    
    // Find room data
    const roomData = Object.values(this.roomCategories)
      .flat()
      .find(room => room.id === roomId);

    if (roomData) {
      this.selectedRoom = {
        id: roomId,
        name: roomData.name,
        emoji: roomData.emoji,
        area: roomArea,
        category: category,
        isCustom: false
      };
    }
  }

  private handleCellClick(cell: HTMLElement): void {
    const row = parseInt(cell.dataset.row!);
    const col = parseInt(cell.dataset.col!);
    const gridCell = this.grid[row][col];

    if (gridCell.isOccupied) {
      // Remove room from cell
      this.removeRoomFromCell(row, col);
    } else if (this.selectedRoom) {
      // Place selected room in cell
      this.placeRoomInCell(row, col, this.selectedRoom);
    }

    this.updateGrid();
    this.updateSpaceUtilization();
    this.emit('roomsChanged', this.rooms);
  }

  private placeRoomInCell(row: number, col: number, room: Room): void {
    const gridCell = this.grid[row][col];
    gridCell.room = { ...room };
    gridCell.isOccupied = true;

    // Add to rooms array if not already present
    const existingRoom = this.rooms.find(r => r.id === room.id);
    if (!existingRoom) {
      this.rooms.push({ ...room });
    }
  }

  private removeRoomFromCell(row: number, col: number): void {
    const gridCell = this.grid[row][col];
    const roomToRemove = gridCell.room;
    
    gridCell.room = undefined;
    gridCell.isOccupied = false;

    // Check if this was the last instance of this room type
    const remainingInstances = this.grid.flat().filter(cell => 
      cell.room && cell.room.id === roomToRemove?.id
    );

    if (remainingInstances.length === 0 && roomToRemove) {
      // Remove from rooms array
      this.rooms = this.rooms.filter(room => room.id !== roomToRemove.id);
    }
  }

  private updateGrid(): void {
    const gridContainer = this.container.querySelector('#floorPlanGrid') as HTMLElement;
    gridContainer.innerHTML = this.renderGrid();
    
    // Re-attach event listeners to new cells
    gridContainer.querySelectorAll('.grid-cell').forEach(cell => {
      cell.addEventListener('click', () => this.handleCellClick(cell as HTMLElement));
      cell.addEventListener('keydown', (e: Event) => {
        const keyEvent = e as KeyboardEvent;
        if (keyEvent.key === 'Enter' || keyEvent.key === ' ') {
          e.preventDefault();
          this.handleCellClick(cell as HTMLElement);
        }
      });
    });
  }

  private updateSpaceUtilization(): void {
    const occupiedCells = this.grid.flat().filter(cell => cell.isOccupied);
    const totalArea = occupiedCells.reduce((sum, cell) => sum + (cell.room?.area || 0), 0);
    const uniqueRooms = new Set(occupiedCells.map(cell => cell.room?.id)).size;

    // Update UI
    const totalRoomsEl = this.container.querySelector('#totalRooms') as HTMLElement;
    const totalAreaEl = this.container.querySelector('#totalArea') as HTMLElement;
    const spaceEfficiencyEl = this.container.querySelector('#spaceEfficiency') as HTMLElement;

    if (totalRoomsEl) totalRoomsEl.textContent = uniqueRooms.toString();
    if (totalAreaEl) totalAreaEl.textContent = `${totalArea.toLocaleString()} sq ft`;
    if (spaceEfficiencyEl) {
      const efficiency = Math.min((occupiedCells.length / (this.gridSize.rows * this.gridSize.cols)) * 100, 100);
      spaceEfficiencyEl.textContent = `${efficiency.toFixed(1)}%`;
    }
  }

  private filterRooms(searchTerm: string): void {
    const term = searchTerm.toLowerCase();
    
    this.container.querySelectorAll('.room-option').forEach(option => {
      const roomName = option.querySelector('.room-name')?.textContent?.toLowerCase() || '';
      const isMatch = roomName.includes(term);
      
      (option as HTMLElement).style.display = isMatch ? 'flex' : 'none';
      option.classList.toggle('search-match', isMatch && term.length > 0);
    });
  }

  private editRoomSize(roomId: string): void {
    const roomData = Object.values(this.roomCategories)
      .flat()
      .find(room => room.id === roomId);

    if (!roomData) return;

    const currentArea = roomData.area;
    const newArea = prompt(
      `${t('calculator.labels.edit')} ${roomData.name} ${t('calculator.labels.area')}:`,
      currentArea.toString()
    );

    if (newArea && !isNaN(parseInt(newArea))) {
      const numericArea = parseInt(newArea);
      if (numericArea > 0 && numericArea <= 10000) {
        // Update room data
        roomData.area = numericArea;
        
        // Update all instances in grid
        this.grid.flat().forEach(cell => {
          if (cell.room && cell.room.id === roomId) {
            cell.room.area = numericArea;
          }
        });

        // Update rooms array
        const roomInArray = this.rooms.find(room => room.id === roomId);
        if (roomInArray) {
          roomInArray.area = numericArea;
        }

        // Re-render and update
        this.render();
        this.setupEventListeners();
        this.updateSpaceUtilization();
        this.emit('roomsChanged', this.rooms);
      }
    }
  }

  private clearGrid(): void {
    this.grid.forEach(row => {
      row.forEach(cell => {
        cell.room = undefined;
        cell.isOccupied = false;
      });
    });
    
    this.rooms = [];
    this.updateGrid();
    this.updateSpaceUtilization();
    this.emit('roomsChanged', this.rooms);
  }

  private autoArrangeRooms(): void {
    // Simple auto-arrangement algorithm
    this.clearGrid();
    
    const essentialRooms = ['bedroom', 'bathroom', 'kitchen', 'living-room'];
    let cellIndex = 0;
    
    essentialRooms.forEach(roomId => {
      if (cellIndex < this.gridSize.rows * this.gridSize.cols) {
        const row = Math.floor(cellIndex / this.gridSize.cols);
        const col = cellIndex % this.gridSize.cols;
        
        const roomData = Object.values(this.roomCategories)
          .flat()
          .find(room => room.id === roomId);
          
        if (roomData) {
          const room: Room = {
            id: roomId,
            name: roomData.name,
            emoji: roomData.emoji,
            area: roomData.area,
            category: 'essential',
            isCustom: false
          };
          
          this.placeRoomInCell(row, col, room);
          cellIndex++;
        }
      }
    });
    
    this.updateGrid();
    this.updateSpaceUtilization();
    this.emit('roomsChanged', this.rooms);
  }

  private setupDragAndDrop(): void {
    // Implementation for drag and drop functionality
    // This would allow dragging rooms from the panel to the grid
  }

  // Event system
  public on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => callback(data));
    }
  }

  // Public API
  public getRooms(): Room[] {
    return [...this.rooms];
  }

  public setRooms(rooms: Room[]): void {
    this.rooms = [...rooms];
    this.clearGrid();
    
    // Place rooms in grid (simple placement)
    let cellIndex = 0;
    rooms.forEach(room => {
      if (cellIndex < this.gridSize.rows * this.gridSize.cols) {
        const row = Math.floor(cellIndex / this.gridSize.cols);
        const col = cellIndex % this.gridSize.cols;
        this.placeRoomInCell(row, col, room);
        cellIndex++;
      }
    });
    
    this.updateGrid();
    this.updateSpaceUtilization();
  }

  public destroy(): void {
    this.eventListeners.clear();
  }
}
