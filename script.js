// Initialize charts
let spaceChart, materialChart, laborChart;

// Dark Mode Persistence
if (localStorage.getItem('darkMode') === 'enabled') {
    document.body.classList.add('dark-mode');
}

// Global Variables
let previousUnit = 'ft'; // Track previous unit
let gridCells = []; // Grid cells for floor plan
let selectedRoom = null; // Currently selected room for placement
let selectedQuality = 2; // Default to premium quality
let currencySymbol = '₹'; // Default currency symbol

// Unit conversion to feet
const unitToFeet = {
    'ft': 1,
    'yd': 3,
    'in': 0.0833333,
    'm': 3.28084
};

// Base Rates (Update these according to current market rates)
const rates = {
    delhi: { standard: 900, premium: 1100, luxury: 1300 },
    mumbai: { standard: 950, premium: 1200, luxury: 1500 },
    bangalore: { standard: 850, premium: 1050, luxury: 1250 },
    hyderabad: { standard: 800, premium: 1000, luxury: 1200 },
    chandigarh: { standard: 1000, premium: 1200, luxury: 1600 }
};

// Currency Conversion Rates
const currencySymbols = {
    INR: '₹',
    USD: '$',
    EUR: '€',
    GBP: '£'
};

const currencyRates = {
    INR: 1,
    USD: 0.012,
    EUR: 0.011,
    GBP: 0.0096
};

// Room Area Definitions (in sqft)
const roomAreas = {
    '🛏️ Bedroom': 200,
    '🚿 Bathroom': 100,
    '🍳 Kitchen': 150,
    '🛋️ Living Room': 250,
    '🚗 Garage': 300,
    '🌿 Balcony': 50,
    '🗄️ Storage': 50,
    '🍽️ Dining Area': 150,
    '🏋️ Gym': 150, 
    '📚 Study Room': 100, 
    '🎮 Game Room': 200, 
    '🧘 Meditation Room': 100, 
    '☀️ Solar Panels': 100, 
    '🌧️ Rainwater Harvesting': 50, 
    '⚡ Energy-Efficient Appliances': 100,
    '🧺 Laundry Room': 75,
    '💼 Home Office': 120,
    '🥾 Mudroom': 80,
    '🎥 Home Theater': 300,
    '🍷 Wine Cellar': 150,
    '📖 Library': 180,
    '🏊 Indoor Pool': 400,
    '🌍 Geothermal Heating': 200,
    '🌱 Green Roof': 250,
    '🍞 Pantry': 60,
    '👔 Walk-in Closet': 90,
    '👶 Nursery': 150,
    '💆 Spa Room': 200,
    '🎳 Bowling Alley': 500,
    '🎨 Art Studio': 250,
    '🏡 Guest House': 800,
    '🌪️ Wind Turbine': 150,
    '♻️ Composting System': 80,
    '🚨 Panic Room': 120,
    '🎙️ Recording Studio': 300,
    '🎞️ Darkroom': 100,
    '🧖 Sauna': 80,
    '⛪ Chapel': 200,
    '🍵 Tea Ceremony Room': 150,
    '⛺ Yurt': 300,
    '🌿 Hydroponic Farm': 400,
    '💧 Greywater System': 120 
};

// Material Cost Data (per sqft) by city
const materialCosts = {
    delhi: {
        cement: { cost: 50, emoji: '🧱' },
        steel: { cost: 200, emoji: '🔩' },
        bricks: { cost: 30, emoji: '🧱' },
        tiles: { cost: 80, emoji: '🪨' },
        sand: { cost: 20, emoji: '🏖️' },
        paint: { cost: 40, emoji: '🎨' },
        plumbing: { cost: 60, emoji: '🚰' },
        electrical: { cost: 70, emoji: '💡' },
        wood: { cost: 90, emoji: '🪵' },
        glass: { cost: 100, emoji: '🪟' },
        fixtures: { cost: 50, emoji: '🚪' },
        concrete: { cost: 70, emoji: '🏗️' },
        insulation: { cost: 30, emoji: '🧤' },
        roofing: { cost: 120, emoji: '🏠' },
        windows: { cost: 150, emoji: '🪟' },
        doors: { cost: 200, emoji: '🚪' },
        flooring: { cost: 80, emoji: '🪵' }
    },
    mumbai: {
        cement: { cost: 55, emoji: '🧱' },
        steel: { cost: 205, emoji: '🔩' },
        bricks: { cost: 35, emoji: '🧱' },
        tiles: { cost: 85, emoji: '🪨' },
        sand: { cost: 25, emoji: '🏖️' },
        paint: { cost: 45, emoji: '🎨' },
        plumbing: { cost: 65, emoji: '🚰' },
        electrical: { cost: 75, emoji: '💡' },
        wood: { cost: 95, emoji: '🪵' },
        glass: { cost: 105, emoji: '🪟' },
        fixtures: { cost: 55, emoji: '🚪' },
        concrete: { cost: 75, emoji: '🏗️' },
        insulation: { cost: 35, emoji: '🧤' },
        roofing: { cost: 125, emoji: '🏠' },
        windows: { cost: 155, emoji: '🪟' },
        doors: { cost: 205, emoji: '🚪' },
        flooring: { cost: 85, emoji: '🪵' }
    },
    bangalore: {
        cement: { cost: 60, emoji: '🧱' },
        steel: { cost: 210, emoji: '🔩' },
        bricks: { cost: 40, emoji: '🧱' },
        tiles: { cost: 80, emoji: '🪨' },
        sand: { cost: 20, emoji: '🏖️' },
        paint: { cost: 40, emoji: '🎨' },
        plumbing: { cost: 60, emoji: '🚰' },
        electrical: { cost: 70, emoji: '💡' },
        wood: { cost: 90, emoji: '🪵' },
        glass: { cost: 100, emoji: '🪟' },
        fixtures: { cost: 50, emoji: '🚪' },
        concrete: { cost: 70, emoji: '🏗️' },
        insulation: { cost: 30, emoji: '🧤' },
        roofing: { cost: 120, emoji: '🏠' },
        windows: { cost: 150, emoji: '🪟' },
        doors: { cost: 200, emoji: '🚪' },
        flooring: { cost: 80, emoji: '🪵' }
    },
    hyderabad: {
        cement: { cost: 50, emoji: '🧱' },
        steel: { cost: 200, emoji: '🔩' },
        bricks: { cost: 30, emoji: '🧱' },
        tiles: { cost: 80, emoji: '🪨' },
        sand: { cost: 20, emoji: '🏖️' },
        paint: { cost: 40, emoji: '🎨' },
        plumbing: { cost: 60, emoji: '🚰' },
        electrical: { cost: 70, emoji: '💡' },
        wood: { cost: 90, emoji: '🪵' },
        glass: { cost: 100, emoji: '🪟' },
        fixtures: { cost: 50, emoji: '🚪' },
        concrete: { cost: 70, emoji: '🏗️' },
        insulation: { cost: 30, emoji: '🧤' },
        roofing: { cost: 120, emoji: '🏠' },
        windows: { cost: 150, emoji: '🪟' },
        doors: { cost: 200, emoji: '🚪' },
        flooring: { cost: 80, emoji: '🪵' }
    },
    chandigarh: {
        cement: { cost: 50, emoji: '🧱' },
        steel: { cost: 200, emoji: '🔩' },
        bricks: { cost: 30, emoji: '🧱' },
        tiles: { cost: 80, emoji: '🪨' },
        sand: { cost: 20, emoji: '🏖️' },
        paint: { cost: 40, emoji: '🎨' },
        plumbing: { cost: 60, emoji: '🚰' },
        electrical: { cost: 70, emoji: '💡' },
        wood: { cost: 90, emoji: '🪵' },
        glass: { cost: 100, emoji: '🪟' },
        fixtures: { cost: 50, emoji: '🚪' },
        concrete: { cost: 70, emoji: '🏗️' },
        insulation: { cost: 30, emoji: '🧤' },
        roofing: { cost: 120, emoji: '🏠' },
        windows: { cost: 150, emoji: '🪟' },
        doors: { cost: 200, emoji: '🚪' },
        flooring: { cost: 80, emoji: '🪵' }
    }    
};

// Labor Cost Data (per sqft) by city
const laborCosts = {
    delhi: {
        painter: { cost: 30, emoji: '🎨' },
        carpenter: { cost: 50, emoji: '🚰' },
        electrician: { cost: 60, emoji: '🔌' },
        plumber: { cost: 40, emoji: '🚿' },
        mason: { cost: 70, emoji: '🧱' },
        laborer: { cost: 20, emoji: '👷' }
    },
    mumbai: {
        painter: { cost: 35, emoji: '🎨' },
        carpenter: { cost: 55, emoji: '🚰' },
        electrician: { cost: 65, emoji: '🔌' },
        plumber: { cost: 45, emoji: '🚿' },
        mason: { cost: 75, emoji: '🧱' },
        laborer: { cost: 25, emoji: '👷' }
    },
    bangalore: {
        painter: { cost: 30, emoji: '🎨' },
        carpenter: { cost: 50, emoji: '🚰' },
        electrician: { cost: 60, emoji: '🔌' },
        plumber: { cost: 40, emoji: '🚿' },
        mason: { cost: 70, emoji: '🧱' },
        laborer: { cost: 20, emoji: '👷' }
    },
    hyderabad: {
        painter: { cost: 30, emoji: '🎨' },
        carpenter: { cost: 50, emoji: '🚰' },
        electrician: { cost: 60, emoji: '🔌' },
        plumber: { cost: 40, emoji: '🚿' },
        mason: { cost: 70, emoji: '🧱' },
        laborer: { cost: 20, emoji: '👷' }
    }, 
    chandigarh: {
        painter: { cost: 30, emoji: '🎨' },
        carpenter: { cost: 50, emoji: '🚰' },
        electrician: { cost: 60, emoji: '🔌' },
        plumber: { cost: 40, emoji: '🚿' },
        mason: { cost: 70, emoji: '🧱' },
        laborer: { cost: 20, emoji: '👷' }
    }    
};

// Room Key Map for display
const roomKeyMap = {
    'bedroom': '🛏️ Bedroom',
    'bathroom': '🚿 Bathroom',
    'kitchen': '🍳 Kitchen',
    'living-room': '🛋️ Living Room',
    'garage': '🚗 Garage',
    'balcony': '🌿 Balcony',
    'storage': '🗄️ Storage',
    'dining-area': '🍽️ Dining Area',
    'laundry-room': '🧺 Laundry Room',
    'home-office': '💼 Home Office',
    'mudroom': '🥾 Mudroom',
    'pantry': '🍞 Pantry',
    'closet': '👔 Walk-in Closet',
    'nursery': '👶 Nursery',
    'study-room': '📚 Study Room',
    'game-room': '🎮 Game Room',
    'meditation-room': '🧘 Meditation Room',
    'gym': '🏋️ Gym',
    'home-theater': '🎥 Home Theater',
    'wine-cellar': '🍷 Wine Cellar',
    'library': '📖 Library',
    'indoor-pool': '🏊 Indoor Pool',
    'spa-room': '💆 Spa Room',
    'bowling-alley': '🎳 Bowling Alley',
    'art-studio': '🎨 Art Studio',
    'guest-house': '🏡 Guest House',
    'solar-panels': '☀️ Solar Panels',
    'rainwater-harvesting': '🌧️ Rainwater Harvesting',
    'energy-efficient-appliances': '⚡ Energy-Efficient Appliances',
    'geothermal-heating': '🌍 Geothermal Heating',
    'green-roof': '🌱 Green Roof',
    'wind-turbine': '🌪️ Wind Turbine',
    'composting': '♻️ Composting System',
    'hydroponic-farm': '🌿 Hydroponic Farm',
    'greywater-system': '💧 Greywater System',
    'panic-room': '🚨 Panic Room',
    'recording-studio': '🎙️ Recording Studio',
    'darkroom': '🎞️ Darkroom',
    'sauna': '🧖 Sauna',
    'chapel': '⛪ Chapel',
    'tea-ceremony-room': '🍵 Tea Ceremony Room',
    'yurt': '⛺ Yurt'
};

// Function to get length in feet
function getLengthInFeet() {
    const unit = document.getElementById('dimensionUnit').value;
    const value = parseFloat(document.getElementById('length').value) || 0;
    return value * unitToFeet[unit];
}

// Function to get width in feet
function getWidthInFeet() {
    const unit = document.getElementById('dimensionUnit').value;
    const value = parseFloat(document.getElementById('width').value) || 0;
    return value * unitToFeet[unit];
}

// Function to calculate net carpet area
function calculateNetCarpetArea() {
    const length = getLengthInFeet();
    const width = getWidthInFeet();
    const floors = parseInt(document.getElementById('floors').value) || 1;
    const maxPossibleArea = length * width * floors;

    const occupiedCells = gridCells.filter((cell) => cell.classList.contains('occupied'));
    let totalArea = 0;

    occupiedCells.forEach((cell) => {
        const room = cell.textContent;
        if (roomAreas[room]) {
            totalArea += roomAreas[room];
        }
    });

    // Cap the carpet area at the maximum possible plot area
    return Math.min(totalArea, maxPossibleArea);
}

// Function to calculate total room area
function calculateTotalRoomArea() {
    return gridCells.reduce((sum, cell) => {
        if (cell.classList.contains('occupied')) {
            const room = getCleanRoomName(cell.textContent);
            return sum + (roomAreas[room] || 0);
        }
        return sum;
    }, 0);
}

// Function to calculate maximum possible area
function calculateMaxPossibleArea() {
    const length = getLengthInFeet();
    const width = getWidthInFeet();
    const floors = parseInt(document.getElementById('floors').value) || 1;
    return length * width * floors;
}

// Function to convert area to current unit
function convertAreaToCurrentUnit(areaSqft) {
    const unit = document.getElementById('dimensionUnit').value;
    switch (unit) {
        case 'yd': return areaSqft / 9; // Convert sqft to sqyd
        case 'in': return areaSqft * 144; // Convert sqft to sqin
        case 'm': return areaSqft / 10.7639; // Convert sqft to m²
        default: return areaSqft; // Keep as sqft
    }
}

// Function to get area unit label
function getAreaUnitLabel() {
    const unit = document.getElementById('dimensionUnit').value;
    return {
        'ft': 'sqft',
        'yd': 'sqyd', 
        'in': 'sqin',
        'm': 'm²'
    }[unit];
}

// Function to update net area display
function updateNetAreaDisplay() {
    const maxArea = calculateMaxPossibleArea();
    const totalRoomArea = calculateTotalRoomArea();
    const netArea = Math.min(totalRoomArea, maxArea);
    const percentage = maxArea > 0 ? ((netArea / maxArea) * 100).toFixed(1) : 0;
    
    // Convert values
    const convertedNet = convertAreaToCurrentUnit(netArea);
    const unitLabel = getAreaUnitLabel();
    const convertedMax = convertAreaToCurrentUnit(maxArea); // Now used below

    document.getElementById('netCarpetArea').innerHTML = `
        📐 Total Usable Area: ${convertedNet.toLocaleString()} ${unitLabel}
        <span class="area-percentage">
            (${percentage}% of ${convertedMax.toLocaleString()} ${unitLabel})
        </span>
    `;
    // Update warning
    const warningElement = document.getElementById('areaWarning');
    const excessElement = document.getElementById('excessArea');
    if (totalRoomArea > maxArea) {
        const excessConverted = convertAreaToCurrentUnit(totalRoomArea - maxArea);
        warningElement.style.display = 'block';
        excessElement.textContent = `${excessConverted.toLocaleString()} ${unitLabel}`;
    } else {
        warningElement.style.display = 'none';
    }
}

// Function to update space utilization
function updateSpaceUtilization() {
    const occupiedCells = gridCells.filter((cell) => cell.classList.contains('occupied'));
    const spaceDetails = document.getElementById('spaceDetails');
    const spaceAllocation = {};
    const unitLabel = getAreaUnitLabel(); // Get current unit label

    occupiedCells.forEach((cell) => {
        const room = cell.textContent;
        if (!spaceAllocation[room]) {
            spaceAllocation[room] = { count: 0, area: 0 };
        }
        spaceAllocation[room].count++;
        spaceAllocation[room].area += roomAreas[room] || 0;
    });

    // Convert values to current unit
    const totalRoomArea = calculateTotalRoomArea();
    const maxArea = calculateMaxPossibleArea();
    const convertedTotal = convertAreaToCurrentUnit(totalRoomArea);
    const convertedMax = convertAreaToCurrentUnit(maxArea);

    const spaceDetailsHTML = Object.entries(spaceAllocation)
        .map(([room, data]) => {
            const convertedArea = convertAreaToCurrentUnit(data.area);
            return `
                <div class="util-item">
                    <span>${room} (${data.count})</span>
                    <span>${convertedArea.toLocaleString()} ${unitLabel}</span>
                </div>
            `;
        }).join('');

    spaceDetails.innerHTML = spaceDetailsHTML + `
        <div class="util-item total-row">
            <span>Total Used</span>
            <span>${convertedTotal.toLocaleString()} / ${convertedMax.toLocaleString()} ${unitLabel}</span>
        </div>
    `;
}

// Function to calculate construction cost
function calculateCost() {
    // Automatically select essential rooms if no rooms are selected
    const occupiedCells = gridCells.filter((cell) => cell.classList.contains('occupied'));
    if (occupiedCells.length === 0) {
        // Select essential rooms: Bedroom, Bathroom, Kitchen, Living Room
        const essentialRooms = ['🛏️ Bedroom', '🚿 Bathroom', '🍳 Kitchen', '🛋️ Living Room'];
        essentialRooms.forEach((room, index) => {
            const cell = gridCells[index];
            cell.textContent = room;
            cell.classList.add('occupied');
        });

        // Update space utilization and net carpet area after selecting essential rooms
        updateSpaceUtilization();
        const netCarpetArea = calculateNetCarpetArea();
        document.getElementById('netCarpetArea').innerHTML = `
            📐 Total Usable Area: ${netCarpetArea.toLocaleString()} sqft 
            <span class="area-percentage">(${((netCarpetArea / maxPossibleArea) * 100).toFixed(1)}% of plot)</span>
        `;
        
        // Update the chart with the essential rooms
        const length = getLengthInFeet();
        const width = getWidthInFeet();
        const floors = parseInt(document.getElementById('floors').value) || 1;
        updateCharts(length * width * floors); // Pass the plot area to update the chart
        updateNetAreaDisplay();
    }

    // Get input values
    const length = getLengthInFeet();
    const width = getWidthInFeet();
    const floors = parseInt(document.getElementById('floors').value) || 1;
    const city = document.getElementById('city').value;
    const materials = materialCosts[city];
    const labors = laborCosts[city];

    // Calculate plot area
    const plotArea = length * width * floors;

    // Define quality multipliers
    const qualityMultipliers = {
        1: 1.0,  // Standard
        2: 1.25, // Premium
        3: 1.5   // Luxury
    };
    // Get selected quality multiplier
    const qualityMultiplier = qualityMultipliers[selectedQuality] || 1;

    // Get base rate based on selected quality
    let rate;
    switch (selectedQuality) {
        case 1: rate = rates[city].standard; break;
        case 2: rate = rates[city].premium; break;
        case 3: rate = rates[city].luxury; break;
        default: rate = rates[city].standard;
    }

    // Convert rate to selected currency
    const currency = document.getElementById('currency').value;
    rate *= currencyRates[currency];
    currencySymbol = currencySymbols[currency]; // This updates the global symbol

    // Update space utilization
    updateSpaceUtilization();

    // Update net carpet area
    const netCarpetArea = calculateNetCarpetArea();
    updateNetAreaDisplay();

    // Update material cost breakdown
    const materialTable = document.getElementById('materialTable');
    let totalMaterialCost = 0;
    const materialTableHTML = `
        <table>
            <tr>
                <th>Material</th>
                <th>Total Cost</th>
            </tr>
            ${Object.entries(materials).map(([material, data]) => {
                const total = data.cost * qualityMultiplier * netCarpetArea * currencyRates[currency];
                totalMaterialCost += total;
                return `
                    <tr>
                        <td>${data.emoji} ${material.charAt(0).toUpperCase() + material.slice(1)}</td>
                        <td>${currencySymbol} ${total.toLocaleString()}</td>
                    </tr>
                `;
            }).join('')}
        </table>
    `;
    materialTable.innerHTML = materialTableHTML;
    document.getElementById('totalMaterialCost').textContent = `Total Material Cost: ${currencySymbol} ${totalMaterialCost.toLocaleString()}`;

    // Update labor cost breakdown
    const laborTable = document.getElementById('laborTable');
    let totalLaborCost = 0;
    const laborTableHTML = `
        <table>
            <tr>
                <th>Labor Type</th>
                <th>Total Cost</th>
            </tr>
            ${Object.entries(labors).map(([labor, data]) => {
                const total = data.cost * qualityMultiplier * netCarpetArea * currencyRates[currency];
                totalLaborCost += total;
                return `
                    <tr>
                        <td>${data.emoji} ${labor.charAt(0).toUpperCase() + labor.slice(1)}</td>
                        <td>${currencySymbol} ${total.toLocaleString()}</td>
                    </tr>
                `;
            }).join('')}
        </table>
    `;
    laborTable.innerHTML = laborTableHTML;
    document.getElementById('totalLaborCost').textContent = `Total Labor Cost: ${currencySymbol} ${totalLaborCost.toLocaleString()}`;

    // Update charts
    updateCharts(netCarpetArea);

    // Calculate and display time estimate
    calculateTimeEstimate(netCarpetArea);

    const baseCost = totalMaterialCost + totalLaborCost;
    const lowerRange = baseCost * 0.85;  // 15% lower
    const upperRange = baseCost * 1.15;  // 15% higher

    const formatCost = (amount) => {
        if (amount >= 10000000) return `${(amount/10000000).toFixed(1)}Cr`;
        if (amount >= 100000) return `${(amount/100000).toFixed(1)}L`;
        return amount.toLocaleString();
    };

    document.getElementById('totalEstimatedCost').innerHTML = `
        <div class="cost-range-display">
            <div class="range-bar"></div>
            <div class="cost-figures">
                <div class="cost-figure from">
                    <span class="currency">${currencySymbol}</span>
                    <span class="amount">${formatCost(lowerRange)}</span>
                </div>
                <div class="separator">➔</div>
                <div class="cost-figure to">
                    <span class="currency">${currencySymbol}</span>
                    <span class="amount">${formatCost(upperRange)}</span>
                </div>
            </div>
        </div>
        <div class="range-note">
            📊 Inclusive of Material + Labor Costs (±15% variance)
            <div class="sparkles">✨</div>
        </div>
    `;
}

// Function to update charts
function updateCharts(netCarpetArea) {
    // Get current city selection
    const city = document.getElementById('city').value;
    const materials = materialCosts[city];
    const labors = laborCosts[city];

    // Get the occupied cells from the grid
    const occupiedCells = gridCells.filter((cell) => cell.classList.contains('occupied'));

    // Calculate space allocation based on the grid
    const spaceAllocation = {};
    occupiedCells.forEach((cell) => {
        const room = cell.textContent;
        if (!spaceAllocation[room]) spaceAllocation[room] = 0;
        spaceAllocation[room] += roomAreas[room] || 0; 
    });

    // Convert space allocation to an array of objects for the chart
    const spaceData = Object.entries(spaceAllocation).map(([room, area]) => ({
        name: room,
        area: area
    }));

    // Destroy existing chart
    if (spaceChart) spaceChart.destroy();
    if (materialChart) materialChart.destroy();
    if (laborChart) laborChart.destroy();

    // Space Utilization Pie Chart
    const spaceCtx = document.getElementById('spaceChart').getContext('2d');
    spaceChart = new Chart(spaceCtx, {
        type: 'pie',
        data: {
            labels: spaceData.map(item => item.name),
            datasets: [{
                data: spaceData.map(item => item.area),
                backgroundColor: [
                    '#4299e1', '#3182ce', '#2b6cb0', '#2c5282', '#2a4365', '#1a365d', '#4299e1', '#3182ce', '#2b6cb0'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
        }
    });

    // Material Cost Bar Chart
    const materialCtx = document.getElementById('materialChart').getContext('2d');
    materialChart = new Chart(materialCtx, {
        type: 'bar',
        data: {
            labels: Object.keys(materials).map(material => material.charAt(0).toUpperCase() + material.slice(1)),
            datasets: [{
                label: 'Total Cost',
                data: Object.values(materials).map(data => data.cost * netCarpetArea),
                backgroundColor: '#4299e1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Cost'
                    }
                }
            }
        }
    });

    // Labor Cost Bar Chart
    const laborCtx = document.getElementById('laborChart').getContext('2d');
    laborChart = new Chart(laborCtx, {
        type: 'bar',
        data: {
            labels: Object.keys(labors).map(labor => labor.charAt(0).toUpperCase() + labor.slice(1)),
            datasets: [{
                label: 'Total Cost',
                data: Object.values(labors).map(data => data.cost * netCarpetArea),
                backgroundColor: '#4299e1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Cost'
                    }
                }
            }
        }
    });
}

// Function to calculate and display time estimate
function calculateTimeEstimate(netCarpetArea) {
    // Time estimate factors (in days per 1000 sqft)
    const timeFactors = {
        standard: 60, // Standard quality takes 60 days per 1000 sqft
        premium: 90,  // Premium quality takes 90 days per 1000 sqft
        luxury: 120   // Luxury quality takes 120 days per 1000 sqft
    };

    // Get the selected quality
    let qualityFactor;
    switch (selectedQuality) {
        case 1: qualityFactor = timeFactors.standard; break;
        case 2: qualityFactor = timeFactors.premium; break;
        case 3: qualityFactor = timeFactors.luxury; break;
        default: qualityFactor = timeFactors.standard;
    }

    // Calculate estimated time in days
    const estimatedTimeDays = (netCarpetArea / 1000) * qualityFactor;

    // Convert days to years, months, and days
    const estimatedTimeYears = Math.floor(estimatedTimeDays / 365); // 1 year = 365 days
    const remainingDaysAfterYears = estimatedTimeDays % 365;
    const estimatedTimeMonths = Math.floor(remainingDaysAfterYears / 30); // 1 month = 30 days
    const remainingDays = Math.floor(remainingDaysAfterYears % 30);

    // Display the result
    document.getElementById('timeEstimate').textContent =
        `Estimated Time: ${estimatedTimeYears} years, ${estimatedTimeMonths} months, and ${remainingDays} days`;
}

// Function to edit room size
function editRoomSize(roomKey) {
    const roomName = roomKeyMap[roomKey];
    const currentArea = roomAreas[roomName];
    const newArea = prompt(`Enter new area for ${roomName} (sqft):\nDefault: ${currentArea} sqft`, currentArea);
    
    if (newArea !== null) {
        const numericArea = parseInt(newArea);
        if (!isNaN(numericArea) && numericArea > 0) {
            roomAreas[roomName] = numericArea;
            
            // Update all instances of this room in the grid
            gridCells.forEach(cell => {
                if (cell.textContent === roomName) {
                    cell.setAttribute('data-original-area', numericArea);
                }
            });
            
            // Update button tooltip
            const button = document.querySelector(`[data-room="${roomKey}"]`);
            button.title = button.title.replace(/\d+/, numericArea);
            
            // Visual feedback
            showToast(`✅ ${roomName} size updated to ${numericArea} sqft`);
            
            // Refresh calculations
            updateNetAreaDisplay();
            calculateCost();
        } else if (newArea !== "") {
            showToast('❌ Please enter a valid positive number');
        }
    }
}

// Function to show toast message
function showToast(message) {
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// Function to toggle dark mode
function toggleDarkMode() {
    document.body.classList.toggle('dark-mode');
    localStorage.setItem('darkMode', document.body.classList.contains('dark-mode') ? 'enabled' : 'disabled');
}

// Function to select construction quality
function selectQuality(quality) {
    selectedQuality = quality;
    document.querySelectorAll('.quality-btn').forEach(btn => {
        btn.classList.remove('selected');
    });
    document.querySelector(`.quality-btn[data-quality="${quality}"]`).classList.add('selected');
}

// Function to initialize floor plan grid
function initializeFloorPlanGrid() {
    gridCells = [];
    const grid = document.getElementById('floorPlanGrid');
    grid.innerHTML = ''; // Clear the grid

    // Create 10x10 grid cells (50 cells total)
    for (let i = 0; i < 100; i++) {
        const cell = document.createElement('div');
        cell.classList.add('grid-cell');
        cell.setAttribute('data-index', i);
        cell.addEventListener('mouseover', handleCellHover);
        cell.addEventListener('mouseout', handleCellHoverEnd);
        cell.addEventListener('click', () => handleCellClick(cell));
        cell.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                handleCellClick(cell);
            }
        });
        cell.setAttribute('tabindex', '0'); // Make grid cells focusable
        gridCells.push(cell);
        grid.appendChild(cell);
    }
}

// Function to handle cell click
function handleCellClick(cell) {
    if (cell.classList.contains('occupied')) {
        // If the cell is already occupied, unselect it
        cell.textContent = '';
        cell.classList.remove('occupied');
    } else {
        // Place the selected room in the cell
        if (selectedRoom) {
            cell.textContent = selectedRoom;
            cell.classList.add('occupied');
        }
    }
    updateNetAreaDisplay(); 
    updateSpaceUtilization();
}

// Function to get clean room name
function getCleanRoomName(cellText) {
    // Remove any trailing edit button symbols
    return cellText.replace(/📏$/, '').trim();
}

// Function to handle cell hover
function handleCellHover(e) {
    const rect = e.target.getBoundingClientRect();
    gridTooltip.style.left = `${rect.left + window.scrollX + 15}px`;
    gridTooltip.style.top = `${rect.top + window.scrollY + 15}px`;
}

// Function to handle cell hover end
function handleCellHoverEnd() {
    gridTooltip.style.opacity = '0';
}

// Event Listeners
document.querySelectorAll('.quality-btn').forEach(btn => {
    btn.addEventListener('click', () => {
        const quality = parseInt(btn.getAttribute('data-quality'));
        selectQuality(quality);
    });
});

document.querySelectorAll('.room-option').forEach((room) => {
    room.addEventListener('click', () => {
        const roomKey = room.getAttribute('data-room');
        selectedRoom = roomKeyMap[roomKey]; 
        document.querySelectorAll('.room-option').forEach((r) => r.classList.remove('selected'));
        room.classList.add('selected');
    });

    room.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
            const roomKey = room.getAttribute('data-room');
            selectedRoom = roomKeyMap[roomKey];
            document.querySelectorAll('.room-option').forEach((r) => r.classList.remove('selected'));
            room.classList.add('selected');
        }
    });
});

document.getElementById('length').addEventListener('input', updateNetAreaDisplay);
document.getElementById('width').addEventListener('input', updateNetAreaDisplay);
document.getElementById('floors').addEventListener('change', updateNetAreaDisplay);
document.getElementById('currency').addEventListener('change', calculateCost);

document.getElementById('city').addEventListener('change', () => {
    calculateCost();
    updateCharts(calculateMaxPossibleArea());
});

document.getElementById('length').addEventListener('keydown', preventNegative);
document.getElementById('width').addEventListener('keydown', preventNegative);

function preventNegative(e) {
    if (e.key === '-' || e.key === 'e' || e.key === 'E') {
        e.preventDefault();
    }
}

document.getElementById('dimensionUnit').addEventListener('change', () => {
    const newUnit = document.getElementById('dimensionUnit').value;
    const lengthInput = document.getElementById('length');
    const widthInput = document.getElementById('width');

    // Convert length
    const lengthValue = parseFloat(lengthInput.value) || 0;
    const lengthInFeet = lengthValue * unitToFeet[previousUnit];
    lengthInput.value = (lengthInFeet / unitToFeet[newUnit]).toFixed(2);

    // Convert width
    const widthValue = parseFloat(widthInput.value) || 0;
    const widthInFeet = widthValue * unitToFeet[previousUnit];
    widthInput.value = (widthInFeet / unitToFeet[newUnit]).toFixed(2);

    // Update previous unit
    previousUnit = newUnit;

    updateNetAreaDisplay();
    calculateCost();
});

document.getElementById('roomSearch').addEventListener('input', (e) => {
    const searchTerm = e.target.value.toLowerCase();
    document.querySelectorAll('.room-option').forEach(option => {
        const roomName = option.textContent.toLowerCase();
        const isMatch = roomName.includes(searchTerm);
        option.classList.toggle('search-match', isMatch);
        option.style.display = isMatch ? 'flex' : 'none';
    });
});

// Initialize the grid when the page loads
window.onload = () => {
    previousUnit = document.getElementById('dimensionUnit').value; // Initialize unit
    initializeFloorPlanGrid();
    selectQuality(2); // Default to premium
    calculateCost();
};