import { I18n<PERSON><PERSON><PERSON>, SUPPORTED_LANGUAGES } from '@/i18n';
import { t } from '@/i18n/index.js';

export class LanguageSwitcher {
  private container: HTMLElement;
  private currentLanguage: string;
  private isOpen: boolean = false;

  constructor(container: HTMLElement) {
    this.container = container;
    this.currentLanguage = I18nHelper.getCurrentLanguage();
    this.initialize();
  }

  private initialize(): void {
    this.render();
    this.setupEventListeners();
  }

  private render(): void {
    const currentLangData = SUPPORTED_LANGUAGES[this.currentLanguage as keyof typeof SUPPORTED_LANGUAGES];
    
    this.container.innerHTML = `
      <div class="language-switcher">
        <button 
          class="language-toggle"
          aria-expanded="${this.isOpen}"
          aria-haspopup="listbox"
          aria-label="${t('accessibility.toggle_language')}"
          id="languageToggle"
        >
          <span class="current-language">
            <span class="language-flag">${currentLangData?.flag || '🌐'}</span>
            <span class="language-code">${this.currentLanguage.toUpperCase()}</span>
          </span>
          <span class="dropdown-arrow" aria-hidden="true">▼</span>
        </button>
        
        <div 
          class="language-dropdown ${this.isOpen ? 'open' : ''}"
          role="listbox"
          aria-labelledby="languageToggle"
          aria-hidden="${!this.isOpen}"
        >
          ${Object.entries(SUPPORTED_LANGUAGES).map(([code, data]) => `
            <button
              class="language-option ${code === this.currentLanguage ? 'selected' : ''}"
              role="option"
              aria-selected="${code === this.currentLanguage}"
              data-language="${code}"
              tabindex="${this.isOpen ? '0' : '-1'}"
            >
              <span class="language-flag">${data.flag}</span>
              <span class="language-name">${data.name}</span>
              <span class="language-code">${code.toUpperCase()}</span>
              ${code === this.currentLanguage ? '<span class="checkmark" aria-hidden="true">✓</span>' : ''}
            </button>
          `).join('')}
        </div>
      </div>
    `;
  }

  private setupEventListeners(): void {
    const toggle = this.container.querySelector('.language-toggle') as HTMLButtonElement;
    const dropdown = this.container.querySelector('.language-dropdown') as HTMLElement;
    const options = this.container.querySelectorAll('.language-option') as NodeListOf<HTMLButtonElement>;

    // Toggle dropdown
    toggle.addEventListener('click', (e) => {
      e.stopPropagation();
      this.toggleDropdown();
    });

    // Language selection
    options.forEach(option => {
      option.addEventListener('click', () => {
        const language = option.dataset.language!;
        this.selectLanguage(language);
      });
    });

    // Keyboard navigation
    toggle.addEventListener('keydown', (e) => {
      switch (e.key) {
        case 'Enter':
        case ' ':
        case 'ArrowDown':
          e.preventDefault();
          this.openDropdown();
          this.focusFirstOption();
          break;
        case 'ArrowUp':
          e.preventDefault();
          this.openDropdown();
          this.focusLastOption();
          break;
      }
    });

    dropdown.addEventListener('keydown', (e) => {
      switch (e.key) {
        case 'Escape':
          e.preventDefault();
          this.closeDropdown();
          toggle.focus();
          break;
        case 'ArrowDown':
          e.preventDefault();
          this.focusNextOption();
          break;
        case 'ArrowUp':
          e.preventDefault();
          this.focusPreviousOption();
          break;
        case 'Home':
          e.preventDefault();
          this.focusFirstOption();
          break;
        case 'End':
          e.preventDefault();
          this.focusLastOption();
          break;
        case 'Enter':
        case ' ':
          e.preventDefault();
          const focused = document.activeElement as HTMLButtonElement;
          if (focused && focused.classList.contains('language-option')) {
            focused.click();
          }
          break;
      }
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', (e) => {
      if (!this.container.contains(e.target as Node)) {
        this.closeDropdown();
      }
    });

    // Handle language change events
    window.addEventListener('languageChanged', () => {
      this.currentLanguage = I18nHelper.getCurrentLanguage();
      this.render();
      this.setupEventListeners();
    });
  }

  private toggleDropdown(): void {
    if (this.isOpen) {
      this.closeDropdown();
    } else {
      this.openDropdown();
    }
  }

  private openDropdown(): void {
    this.isOpen = true;
    this.updateDropdownState();
    
    // Announce to screen readers
    this.announceToScreenReader(t('accessibility.language_menu_opened', { 
      defaultValue: 'Language menu opened' 
    }));
  }

  private closeDropdown(): void {
    this.isOpen = false;
    this.updateDropdownState();
  }

  private updateDropdownState(): void {
    const toggle = this.container.querySelector('.language-toggle') as HTMLButtonElement;
    const dropdown = this.container.querySelector('.language-dropdown') as HTMLElement;
    const options = this.container.querySelectorAll('.language-option') as NodeListOf<HTMLButtonElement>;

    toggle.setAttribute('aria-expanded', this.isOpen.toString());
    dropdown.setAttribute('aria-hidden', (!this.isOpen).toString());
    dropdown.classList.toggle('open', this.isOpen);

    // Update tabindex for options
    options.forEach(option => {
      option.setAttribute('tabindex', this.isOpen ? '0' : '-1');
    });
  }

  private focusFirstOption(): void {
    const firstOption = this.container.querySelector('.language-option') as HTMLButtonElement;
    firstOption?.focus();
  }

  private focusLastOption(): void {
    const options = this.container.querySelectorAll('.language-option');
    const lastOption = options[options.length - 1] as HTMLButtonElement;
    lastOption?.focus();
  }

  private focusNextOption(): void {
    const options = Array.from(this.container.querySelectorAll('.language-option'));
    const currentIndex = options.indexOf(document.activeElement as HTMLButtonElement);
    const nextIndex = (currentIndex + 1) % options.length;
    (options[nextIndex] as HTMLButtonElement).focus();
  }

  private focusPreviousOption(): void {
    const options = Array.from(this.container.querySelectorAll('.language-option'));
    const currentIndex = options.indexOf(document.activeElement as HTMLButtonElement);
    const previousIndex = (currentIndex - 1 + options.length) % options.length;
    (options[previousIndex] as HTMLButtonElement).focus();
  }

  private async selectLanguage(language: string): Promise<void> {
    if (language === this.currentLanguage) {
      this.closeDropdown();
      return;
    }

    try {
      // Show loading state
      this.setLoadingState(true);
      
      // Change language
      await I18nHelper.changeLanguage(language);
      
      this.currentLanguage = language;
      this.closeDropdown();
      
      // Announce language change
      const languageName = SUPPORTED_LANGUAGES[language as keyof typeof SUPPORTED_LANGUAGES]?.name || language;
      this.announceToScreenReader(t('messages.language_changed', { 
        language: languageName 
      }));
      
      // Save preference
      localStorage.setItem('selectedLanguage', language);
      
    } catch (error) {
      console.error('Failed to change language:', error);
      this.announceToScreenReader(t('errors.language_change_failed', {
        defaultValue: 'Failed to change language'
      }));
    } finally {
      this.setLoadingState(false);
    }
  }

  private setLoadingState(loading: boolean): void {
    const toggle = this.container.querySelector('.language-toggle') as HTMLButtonElement;
    toggle.disabled = loading;
    
    if (loading) {
      toggle.classList.add('loading');
      toggle.setAttribute('aria-label', t('status.loading'));
    } else {
      toggle.classList.remove('loading');
      toggle.setAttribute('aria-label', t('accessibility.toggle_language'));
    }
  }

  private announceToScreenReader(message: string): void {
    // Create temporary announcement element
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.style.cssText = `
      position: absolute !important;
      width: 1px !important;
      height: 1px !important;
      padding: 0 !important;
      margin: -1px !important;
      overflow: hidden !important;
      clip: rect(0, 0, 0, 0) !important;
      white-space: nowrap !important;
      border: 0 !important;
    `;
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }

  // Public methods
  public getCurrentLanguage(): string {
    return this.currentLanguage;
  }

  public async setLanguage(language: string): Promise<void> {
    if (SUPPORTED_LANGUAGES[language as keyof typeof SUPPORTED_LANGUAGES]) {
      await this.selectLanguage(language);
    }
  }

  public getSupportedLanguages(): typeof SUPPORTED_LANGUAGES {
    return SUPPORTED_LANGUAGES;
  }

  public destroy(): void {
    // Clean up event listeners
    document.removeEventListener('click', this.closeDropdown);
    window.removeEventListener('languageChanged', this.render);
  }
}

// CSS styles for the language switcher
const styles = `
.language-switcher {
  position: relative;
  display: inline-block;
}

.language-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--bg-secondary, #f8f9fa);
  border: 1px solid var(--border-color, #dee2e6);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.language-toggle:hover {
  background: var(--bg-hover, #e9ecef);
  border-color: var(--border-hover, #adb5bd);
}

.language-toggle:focus {
  outline: 2px solid var(--focus-color, #007bff);
  outline-offset: 2px;
}

.language-toggle.loading {
  opacity: 0.6;
  cursor: not-allowed;
}

.current-language {
  display: flex;
  align-items: center;
  gap: 6px;
}

.language-flag {
  font-size: 16px;
}

.language-code {
  font-weight: 500;
  font-size: 12px;
  text-transform: uppercase;
}

.dropdown-arrow {
  font-size: 10px;
  transition: transform 0.2s ease;
}

.language-toggle[aria-expanded="true"] .dropdown-arrow {
  transform: rotate(180deg);
}

.language-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  min-width: 200px;
  background: var(--bg-primary, #ffffff);
  border: 1px solid var(--border-color, #dee2e6);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  max-height: 300px;
  overflow-y: auto;
}

.language-dropdown.open {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.language-option {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 14px;
}

.language-option:hover {
  background: var(--bg-hover, #f8f9fa);
}

.language-option:focus {
  background: var(--bg-focus, #e3f2fd);
  outline: none;
}

.language-option.selected {
  background: var(--bg-selected, #e3f2fd);
  font-weight: 500;
}

.language-option .language-name {
  flex: 1;
}

.language-option .language-code {
  font-size: 11px;
  color: var(--text-muted, #6c757d);
  text-transform: uppercase;
}

.checkmark {
  color: var(--success-color, #28a745);
  font-weight: bold;
}

.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .language-toggle {
    --bg-secondary: #343a40;
    --border-color: #495057;
    --bg-hover: #495057;
    --border-hover: #6c757d;
  }
  
  .language-dropdown {
    --bg-primary: #212529;
    --border-color: #495057;
  }
  
  .language-option:hover {
    --bg-hover: #343a40;
  }
  
  .language-option:focus {
    --bg-focus: #1e3a5f;
  }
  
  .language-option.selected {
    --bg-selected: #1e3a5f;
  }
}

/* High contrast mode */
.high-contrast .language-toggle {
  border: 2px solid;
  background: ButtonFace;
  color: ButtonText;
}

.high-contrast .language-dropdown {
  border: 2px solid;
  background: Window;
  color: WindowText;
}

.high-contrast .language-option:focus {
  background: Highlight;
  color: HighlightText;
}
`;

// Inject styles
if (!document.getElementById('language-switcher-styles')) {
  const styleSheet = document.createElement('style');
  styleSheet.id = 'language-switcher-styles';
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}
