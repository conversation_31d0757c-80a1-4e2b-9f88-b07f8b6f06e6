// Core Types for Construction Calculator

export type Currency = 'INR' | 'USD' | 'EUR' | 'GBP' | 'CAD' | 'AUD' | 'JPY';
export type QualityLevel = 'standard' | 'premium' | 'luxury';
export type DimensionUnit = 'ft' | 'yd' | 'in' | 'm';
export type RoomCategory = 'essential' | 'luxury' | 'eco' | 'specialized' | 'cultural';

export interface Room {
  id: string;
  name: string;
  emoji: string;
  area: number;
  category: RoomCategory;
  description?: string;
  minArea?: number;
  maxArea?: number;
  isCustom?: boolean;
}

export interface PlotDimensions {
  length: number;
  width: number;
  unit: DimensionUnit;
  floors: number;
}

export interface MaterialCost {
  id: string;
  name: string;
  emoji: string;
  costPerSqft: number;
  unit: string;
  category: string;
}

export interface LaborCost {
  id: string;
  name: string;
  emoji: string;
  costPerSqft: number;
  skillLevel: 'basic' | 'skilled' | 'expert';
}

export interface CityData {
  id: string;
  name: string;
  country: string;
  region: string;
  materials: MaterialCost[];
  labor: LaborCost[];
  baseRates: {
    standard: number;
    premium: number;
    luxury: number;
  };
  regulations?: {
    minSetback?: number;
    maxHeight?: number;
    farRatio?: number;
  };
}

export interface CostBreakdown {
  materials: {
    items: Array<MaterialCost & { total: number }>;
    subtotal: number;
  };
  labor: {
    items: Array<LaborCost & { total: number }>;
    subtotal: number;
  };
  total: number;
  currency: Currency;
  quality: QualityLevel;
  area: number;
  pricePerSqft: number;
}

export interface CalculatorConfig {
  city: string;
  currency: Currency;
  quality: QualityLevel;
  dimensions: PlotDimensions;
  rooms: Room[];
  customRates?: {
    materials?: Partial<Record<string, number>>;
    labor?: Partial<Record<string, number>>;
  };
}

export interface FloorPlanCell {
  id: string;
  x: number;
  y: number;
  room?: Room;
  isOccupied: boolean;
}

export interface FloorPlan {
  id: string;
  name: string;
  dimensions: PlotDimensions;
  grid: FloorPlanCell[][];
  rooms: Room[];
  totalArea: number;
  usedArea: number;
  efficiency: number;
}

export interface TimeEstimate {
  phases: Array<{
    name: string;
    duration: number; // in days
    dependencies: string[];
  }>;
  totalDays: number;
  totalMonths: number;
  totalYears: number;
  criticalPath: string[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface CalculationResult {
  isValid: boolean;
  errors: ValidationError[];
  costBreakdown?: CostBreakdown;
  timeEstimate?: TimeEstimate;
  floorPlan?: FloorPlan;
  recommendations?: string[];
}

// API Response Types
export interface CurrencyRateResponse {
  base: string;
  date: string;
  rates: Record<Currency, number>;
}

export interface MarketDataResponse {
  city: string;
  date: string;
  materials: MaterialCost[];
  labor: LaborCost[];
  trends: {
    material: 'up' | 'down' | 'stable';
    labor: 'up' | 'down' | 'stable';
    percentage: number;
  };
}

// Event Types
export interface CalculatorEvent {
  type: 'calculation' | 'room_added' | 'room_removed' | 'dimension_changed' | 'quality_changed';
  data: any;
  timestamp: number;
}

// Configuration Types
export interface RegionConfig {
  id: string;
  name: string;
  countries: string[];
  defaultCurrency: Currency;
  apiEndpoint: string;
  supportedLanguages: string[];
  cities: CityData[];
}

export interface AppConfig {
  version: string;
  regions: RegionConfig[];
  defaultRegion: string;
  features: {
    ai_suggestions: boolean;
    real_time_pricing: boolean;
    three_d_visualization: boolean;
    export_pdf: boolean;
    collaboration: boolean;
  };
  limits: {
    maxRooms: number;
    maxArea: number;
    maxFloors: number;
  };
}
