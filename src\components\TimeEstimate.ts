import type { TimeEstimate } from '@/types/calculator';
import { t } from '@/i18n/index.js';

export class TimeEstimateComponent {
  private container: HTMLElement;
  private timeData: TimeEstimate | null = null;

  constructor(container: HTMLElement) {
    this.container = container;
    this.initialize();
  }

  private initialize(): void {
    this.render();
  }

  private render(): void {
    this.container.innerHTML = `
      <div class="time-estimate-container">
        <!-- Timeline Overview -->
        <div class="timeline-overview">
          <h2 class="section-title">${t('calculator.time_estimate.title', { defaultValue: 'Construction Timeline' })}</h2>
          <div class="timeline-summary" id="timelineSummary">
            <div class="summary-item">
              <div class="summary-label">${t('calculator.time_estimate.total_duration')}</div>
              <div class="summary-value" id="totalDuration">--</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">${t('calculator.time_estimate.total_phases')}</div>
              <div class="summary-value" id="totalPhases">--</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">${t('calculator.time_estimate.critical_path')}</div>
              <div class="summary-value" id="criticalPathDuration">--</div>
            </div>
          </div>
        </div>

        <!-- Phase Timeline -->
        <div class="phase-timeline">
          <h3>${t('calculator.time_estimate.construction_phases')}</h3>
          <div class="timeline-container" id="timelineContainer">
            <!-- Dynamic timeline content -->
          </div>
        </div>

        <!-- Phase Details -->
        <div class="phase-details">
          <h3>${t('calculator.time_estimate.phase_breakdown')}</h3>
          <div class="phases-table-container">
            <table class="phases-table" id="phasesTable">
              <thead>
                <tr>
                  <th>${t('calculator.labels.phase')}</th>
                  <th>${t('calculator.labels.duration')}</th>
                  <th>${t('calculator.labels.dependencies')}</th>
                  <th>${t('calculator.labels.status')}</th>
                </tr>
              </thead>
              <tbody id="phasesTableBody">
                <!-- Dynamic content -->
              </tbody>
            </table>
          </div>
        </div>

        <!-- Critical Path -->
        <div class="critical-path-section">
          <h3>${t('calculator.time_estimate.critical_path')}</h3>
          <div class="critical-path-visualization" id="criticalPathViz">
            <!-- Dynamic critical path visualization -->
          </div>
        </div>

        <!-- Timeline Insights -->
        <div class="timeline-insights">
          <h3>💡 ${t('calculator.time_estimate.insights')}</h3>
          <div class="insights-list" id="timelineInsights">
            <!-- Dynamic insights -->
          </div>
        </div>
      </div>
    `;
  }

  public update(timeData: TimeEstimate): void {
    this.timeData = timeData;
    this.updateSummary();
    this.updateTimeline();
    this.updatePhasesTable();
    this.updateCriticalPath();
    this.updateInsights();
  }

  private updateSummary(): void {
    if (!this.timeData) return;

    const totalDurationEl = this.container.querySelector('#totalDuration') as HTMLElement;
    const totalPhasesEl = this.container.querySelector('#totalPhases') as HTMLElement;
    const criticalPathDurationEl = this.container.querySelector('#criticalPathDuration') as HTMLElement;

    if (totalDurationEl) {
      const duration = this.formatDuration(this.timeData.totalDays);
      totalDurationEl.textContent = duration;
    }

    if (totalPhasesEl) {
      totalPhasesEl.textContent = this.timeData.phases.length.toString();
    }

    if (criticalPathDurationEl) {
      const criticalDuration = this.calculateCriticalPathDuration();
      criticalPathDurationEl.textContent = this.formatDuration(criticalDuration);
    }
  }

  private updateTimeline(): void {
    if (!this.timeData) return;

    const timelineContainer = this.container.querySelector('#timelineContainer') as HTMLElement;
    
    let currentDay = 0;
    const timelineHTML = this.timeData.phases.map(phase => {
      const startDay = currentDay;
      const endDay = currentDay + phase.duration;
      currentDay = endDay;

      const progressPercent = (phase.duration / this.timeData!.totalDays) * 100;

      return `
        <div class="timeline-phase" data-phase="${phase.name}">
          <div class="phase-bar" style="width: ${progressPercent}%">
            <div class="phase-info">
              <span class="phase-name">${phase.name}</span>
              <span class="phase-duration">${phase.duration} days</span>
            </div>
          </div>
          <div class="phase-dates">
            Day ${startDay + 1} - ${endDay}
          </div>
        </div>
      `;
    }).join('');

    timelineContainer.innerHTML = timelineHTML;
  }

  private updatePhasesTable(): void {
    if (!this.timeData) return;

    const tbody = this.container.querySelector('#phasesTableBody') as HTMLElement;
    
    tbody.innerHTML = this.timeData.phases.map(phase => `
      <tr>
        <td>
          <div class="phase-cell">
            <span class="phase-name">${phase.name}</span>
          </div>
        </td>
        <td>
          <span class="duration-badge">${phase.duration} days</span>
        </td>
        <td>
          <div class="dependencies">
            ${phase.dependencies ? phase.dependencies.map(dep => 
              `<span class="dependency-tag">${dep}</span>`
            ).join('') : '<span class="no-dependencies">None</span>'}
          </div>
        </td>
        <td>
          <span class="status-badge status-pending">Pending</span>
        </td>
      </tr>
    `).join('');
  }

  private updateCriticalPath(): void {
    if (!this.timeData) return;

    const criticalPathViz = this.container.querySelector('#criticalPathViz') as HTMLElement;
    
    const criticalPathHTML = this.timeData.criticalPath.map((phaseName, index) => {
      const phase = this.timeData!.phases.find(p => p.name === phaseName);
      const isLast = index === this.timeData!.criticalPath.length - 1;

      return `
        <div class="critical-phase">
          <div class="phase-node">
            <span class="phase-number">${index + 1}</span>
            <span class="phase-name">${phaseName}</span>
            <span class="phase-duration">${phase?.duration || 0}d</span>
          </div>
          ${!isLast ? '<div class="phase-connector">→</div>' : ''}
        </div>
      `;
    }).join('');

    criticalPathViz.innerHTML = `
      <div class="critical-path-flow">
        ${criticalPathHTML}
      </div>
    `;
  }

  private updateInsights(): void {
    if (!this.timeData) return;

    const insights: string[] = [];

    // Timeline insights
    if (this.timeData.totalDays > 365) {
      insights.push('🗓️ This is a long-term project spanning over a year. Consider seasonal factors and weather delays.');
    }

    if (this.timeData.phases.length > 8) {
      insights.push('📋 Complex project with many phases. Ensure proper project management and coordination.');
    }

    const longestPhase = this.timeData.phases.reduce((prev, current) => 
      prev.duration > current.duration ? prev : current
    );
    insights.push(`⏱️ The longest phase is "${longestPhase.name}" taking ${longestPhase.duration} days.`);

    // Critical path insights
    const criticalPathDuration = this.calculateCriticalPathDuration();
    const bufferTime = this.timeData.totalDays - criticalPathDuration;
    if (bufferTime > 0) {
      insights.push(`🎯 You have ${bufferTime} days of buffer time outside the critical path.`);
    }

    // Parallel work opportunities
    const parallelPhases = this.timeData.phases.filter(phase => 
      !this.timeData!.criticalPath.includes(phase.name)
    );
    if (parallelPhases.length > 0) {
      insights.push(`⚡ ${parallelPhases.length} phases can potentially run in parallel to save time.`);
    }

    const insightsContainer = this.container.querySelector('#timelineInsights') as HTMLElement;
    insightsContainer.innerHTML = insights.map(insight => `
      <div class="insight-item">${insight}</div>
    `).join('');
  }

  private formatDuration(days: number): string {
    if (days < 30) {
      return `${days} days`;
    } else if (days < 365) {
      const months = Math.round(days / 30);
      return `${months} month${months > 1 ? 's' : ''} (${days} days)`;
    } else {
      const years = Math.floor(days / 365);
      const remainingMonths = Math.round((days % 365) / 30);
      return `${years} year${years > 1 ? 's' : ''} ${remainingMonths} month${remainingMonths > 1 ? 's' : ''} (${days} days)`;
    }
  }

  private calculateCriticalPathDuration(): number {
    if (!this.timeData) return 0;

    return this.timeData.criticalPath.reduce((total, phaseName) => {
      const phase = this.timeData!.phases.find(p => p.name === phaseName);
      return total + (phase?.duration || 0);
    }, 0);
  }

  public destroy(): void {
    // Cleanup if needed
  }
}
