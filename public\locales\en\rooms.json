{"categories": {"essential": "Essential Rooms", "luxury": "Luxury Spaces", "eco": "Eco Features", "specialized": "Specialized Spaces", "cultural": "Cultural Spaces"}, "bedroom": {"name": "Bedroom", "description": "Private sleeping and resting area", "tips": "Consider natural light and ventilation"}, "bathroom": {"name": "Bathroom", "description": "Bathing and personal hygiene area", "tips": "Ensure proper waterproofing and ventilation"}, "kitchen": {"name": "Kitchen", "description": "Food preparation and cooking area", "tips": "Plan for adequate storage and work triangle"}, "living-room": {"name": "Living Room", "description": "Main family gathering and entertainment space", "tips": "Central location with good natural light"}, "garage": {"name": "Garage", "description": "Vehicle storage and workshop space", "tips": "Consider electrical outlets for EV charging"}, "balcony": {"name": "Balcony", "description": "Outdoor relaxation and garden space", "tips": "Ensure proper drainage and safety railings"}, "storage": {"name": "Storage", "description": "General storage and utility space", "tips": "Include shelving and easy access"}, "dining-area": {"name": "Dining Area", "description": "Formal dining and family meals space", "tips": "Adjacent to kitchen for convenience"}, "laundry-room": {"name": "Laundry Room", "description": "Washing and clothes care area", "tips": "Include utility sink and drying space"}, "home-office": {"name": "Home Office", "description": "Work from home and study space", "tips": "Quiet location with good internet connectivity"}, "mudroom": {"name": "<PERSON><PERSON><PERSON>", "description": "Entry transition and storage space", "tips": "Include coat hooks and shoe storage"}, "pantry": {"name": "Pantry", "description": "Food storage and kitchen overflow", "tips": "Cool, dry location with good ventilation"}, "closet": {"name": "Walk-in Closet", "description": "Clothing and accessory storage", "tips": "Include good lighting and organization systems"}, "nursery": {"name": "Nursery", "description": "Baby and child care room", "tips": "Safe, quiet location near master bedroom"}, "study-room": {"name": "Study Room", "description": "Quiet study and reading space", "tips": "Good lighting and minimal distractions"}, "game-room": {"name": "Game Room", "description": "Entertainment and recreation space", "tips": "Soundproofing for noise control"}, "meditation-room": {"name": "Meditation Room", "description": "Peaceful relaxation and mindfulness space", "tips": "Quiet location with natural elements"}, "gym": {"name": "Gym", "description": "Exercise and fitness equipment area", "tips": "Proper ventilation and flooring"}, "home-theater": {"name": "Home Theater", "description": "Movie watching and entertainment space", "tips": "Soundproofing and controlled lighting"}, "wine-cellar": {"name": "Wine Cellar", "description": "Wine storage and tasting area", "tips": "Temperature and humidity control"}, "library": {"name": "Library", "description": "Book collection and reading space", "tips": "Built-in shelving and comfortable seating"}, "indoor-pool": {"name": "Indoor Pool", "description": "Swimming and aquatic exercise area", "tips": "Proper ventilation and safety features"}, "spa-room": {"name": "Spa Room", "description": "Wellness and relaxation treatments", "tips": "Privacy and calming atmosphere"}, "bowling-alley": {"name": "Bowling Alley", "description": "Indoor bowling and recreation", "tips": "Proper lane specifications and safety"}, "art-studio": {"name": "Art Studio", "description": "Creative work and artistic projects", "tips": "North-facing windows for consistent light"}, "guest-house": {"name": "Guest House", "description": "Separate accommodation for visitors", "tips": "Independent utilities and privacy"}, "solar-panels": {"name": "Solar Panels", "description": "Renewable energy generation system", "tips": "South-facing roof with minimal shading"}, "rainwater-harvesting": {"name": "Rainwater Harvesting", "description": "Water conservation and collection system", "tips": "Proper filtration and storage"}, "energy-efficient-appliances": {"name": "Energy-Efficient Appliances", "description": "High-efficiency electrical systems", "tips": "Look for ENERGY STAR ratings"}, "geothermal-heating": {"name": "Geothermal Heating", "description": "Earth-based heating and cooling system", "tips": "Requires ground loop installation"}, "green-roof": {"name": "<PERSON>", "description": "Vegetated roofing system", "tips": "Structural support and drainage required"}, "wind-turbine": {"name": "Wind Turbine", "description": "Wind-powered energy generation", "tips": "Check local zoning and wind patterns"}, "composting": {"name": "Composting System", "description": "Organic waste processing system", "tips": "Proper ventilation and access"}, "hydroponic-farm": {"name": "Hydroponic Farm", "description": "Soil-less agriculture system", "tips": "Controlled environment and lighting"}, "greywater-system": {"name": "Greywater System", "description": "Water recycling and reuse system", "tips": "Separate plumbing and treatment"}, "panic-room": {"name": "Panic Room", "description": "Secure emergency shelter space", "tips": "Reinforced construction and communication"}, "recording-studio": {"name": "Recording Studio", "description": "Soundproof music production space", "tips": "Acoustic treatment and isolation"}, "darkroom": {"name": "Darkroom", "description": "Photography development space", "tips": "Light-tight and ventilation systems"}, "sauna": {"name": "Sauna", "description": "Steam and heat therapy room", "tips": "Proper ventilation and safety features"}, "chapel": {"name": "Chapel", "description": "Small worship and meditation space", "tips": "Peaceful atmosphere and acoustics"}, "tea-ceremony-room": {"name": "Tea Ceremony Room", "description": "Traditional tea preparation space", "tips": "Authentic materials and layout"}, "yurt": {"name": "Yurt", "description": "Circular traditional living space", "tips": "Proper foundation and weather protection"}}