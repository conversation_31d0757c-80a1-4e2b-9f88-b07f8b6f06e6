import { t } from '../i18n/index';

export class ThemeToggle {
  private container: HTMLElement;
  private currentTheme: 'light' | 'dark' | 'auto';
  private systemPreference: 'light' | 'dark';
  private mediaQuery: MediaQueryList;

  constructor(container: HTMLElement) {
    this.container = container;
    this.mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    this.systemPreference = this.mediaQuery.matches ? 'dark' : 'light';
    this.currentTheme = this.getStoredTheme();
    
    this.initialize();
  }

  private initialize(): void {
    this.setupMediaQueryListener();
    this.applyTheme();
    this.render();
    this.setupEventListeners();
  }

  private getStoredTheme(): 'light' | 'dark' | 'auto' {
    const stored = localStorage.getItem('theme') as 'light' | 'dark' | 'auto';
    return stored || 'auto';
  }

  private setupMediaQueryListener(): void {
    this.mediaQuery.addEventListener('change', (e) => {
      this.systemPreference = e.matches ? 'dark' : 'light';
      if (this.currentTheme === 'auto') {
        this.applyTheme();
      }
    });
  }

  private render(): void {
    const themeIcon = this.getThemeIcon();
    const themeLabel = this.getThemeLabel();
    
    this.container.innerHTML = `
      <div class="theme-toggle">
        <button 
          class="theme-toggle-btn"
          aria-label="${t('accessibility.toggle_theme')}"
          title="${themeLabel}"
          id="themeToggleBtn"
        >
          <span class="theme-icon" aria-hidden="true">${themeIcon}</span>
          <span class="theme-label sr-only">${themeLabel}</span>
        </button>
        
        <div class="theme-dropdown" id="themeDropdown" aria-hidden="true">
          <div class="theme-dropdown-header">
            <span class="dropdown-title">${t('settings.theme', { defaultValue: 'Theme' })}</span>
          </div>
          
          <div class="theme-options" role="radiogroup" aria-labelledby="themeToggleBtn">
            <button 
              class="theme-option ${this.currentTheme === 'light' ? 'selected' : ''}"
              role="radio"
              aria-checked="${this.currentTheme === 'light'}"
              data-theme="light"
            >
              <span class="option-icon" aria-hidden="true">☀️</span>
              <span class="option-label">${t('theme.light', { defaultValue: 'Light' })}</span>
              ${this.currentTheme === 'light' ? '<span class="checkmark" aria-hidden="true">✓</span>' : ''}
            </button>
            
            <button 
              class="theme-option ${this.currentTheme === 'dark' ? 'selected' : ''}"
              role="radio"
              aria-checked="${this.currentTheme === 'dark'}"
              data-theme="dark"
            >
              <span class="option-icon" aria-hidden="true">🌙</span>
              <span class="option-label">${t('theme.dark', { defaultValue: 'Dark' })}</span>
              ${this.currentTheme === 'dark' ? '<span class="checkmark" aria-hidden="true">✓</span>' : ''}
            </button>
            
            <button 
              class="theme-option ${this.currentTheme === 'auto' ? 'selected' : ''}"
              role="radio"
              aria-checked="${this.currentTheme === 'auto'}"
              data-theme="auto"
            >
              <span class="option-icon" aria-hidden="true">🔄</span>
              <span class="option-label">${t('theme.auto', { defaultValue: 'Auto' })}</span>
              <span class="option-description">${t('theme.auto_description', { defaultValue: 'Follow system' })}</span>
              ${this.currentTheme === 'auto' ? '<span class="checkmark" aria-hidden="true">✓</span>' : ''}
            </button>
          </div>
        </div>
      </div>
    `;
  }

  private setupEventListeners(): void {
    const toggleBtn = this.container.querySelector('.theme-toggle-btn') as HTMLButtonElement;
    const dropdown = this.container.querySelector('.theme-dropdown') as HTMLElement;
    const options = this.container.querySelectorAll('.theme-option') as NodeListOf<HTMLButtonElement>;

    // Toggle dropdown
    toggleBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      this.toggleDropdown();
    });

    // Theme selection
    options.forEach(option => {
      option.addEventListener('click', () => {
        const theme = option.dataset.theme as 'light' | 'dark' | 'auto';
        this.setTheme(theme);
      });
    });

    // Keyboard navigation
    toggleBtn.addEventListener('keydown', (e) => {
      switch (e.key) {
        case 'Enter':
        case ' ':
        case 'ArrowDown':
          e.preventDefault();
          this.openDropdown();
          this.focusFirstOption();
          break;
      }
    });

    dropdown.addEventListener('keydown', (e) => {
      switch (e.key) {
        case 'Escape':
          e.preventDefault();
          this.closeDropdown();
          toggleBtn.focus();
          break;
        case 'ArrowDown':
          e.preventDefault();
          this.focusNextOption();
          break;
        case 'ArrowUp':
          e.preventDefault();
          this.focusPreviousOption();
          break;
        case 'Enter':
        case ' ':
          e.preventDefault();
          const focused = document.activeElement as HTMLButtonElement;
          if (focused && focused.classList.contains('theme-option')) {
            focused.click();
          }
          break;
      }
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', (e) => {
      if (!this.container.contains(e.target as Node)) {
        this.closeDropdown();
      }
    });

    // Quick toggle on double-click
    toggleBtn.addEventListener('dblclick', () => {
      this.quickToggle();
    });
  }

  private getThemeIcon(): string {
    const effectiveTheme = this.getEffectiveTheme();
    switch (effectiveTheme) {
      case 'dark':
        return '🌙';
      case 'light':
        return '☀️';
      default:
        return '🔄';
    }
  }

  private getThemeLabel(): string {
    switch (this.currentTheme) {
      case 'light':
        return t('theme.light', { defaultValue: 'Light theme' });
      case 'dark':
        return t('theme.dark', { defaultValue: 'Dark theme' });
      case 'auto':
        return t('theme.auto', { defaultValue: 'Auto theme' });
      default:
        return t('theme.unknown', { defaultValue: 'Theme' });
    }
  }

  private getEffectiveTheme(): 'light' | 'dark' {
    if (this.currentTheme === 'auto') {
      return this.systemPreference;
    }
    return this.currentTheme;
  }

  private applyTheme(): void {
    const effectiveTheme = this.getEffectiveTheme();
    
    // Update document classes
    document.documentElement.classList.remove('light-theme', 'dark-theme');
    document.documentElement.classList.add(`${effectiveTheme}-theme`);
    
    // Update body class for backward compatibility
    document.body.classList.toggle('dark-mode', effectiveTheme === 'dark');
    
    // Update meta theme-color
    this.updateThemeColor(effectiveTheme);
    
    // Announce theme change
    this.announceThemeChange(effectiveTheme);
  }

  private updateThemeColor(theme: 'light' | 'dark'): void {
    const themeColorMeta = document.querySelector('meta[name="theme-color"]') as HTMLMetaElement;
    const color = theme === 'dark' ? '#1a1a1a' : '#ffffff';
    
    if (themeColorMeta) {
      themeColorMeta.content = color;
    } else {
      const meta = document.createElement('meta');
      meta.name = 'theme-color';
      meta.content = color;
      document.head.appendChild(meta);
    }
  }

  private announceThemeChange(theme: 'light' | 'dark'): void {
    const message = t('messages.theme_changed', { 
      theme: t(`theme.${theme}`, { defaultValue: theme }),
      defaultValue: `Theme changed to ${theme}`
    });
    
    this.announceToScreenReader(message);
  }

  private toggleDropdown(): void {
    const dropdown = this.container.querySelector('.theme-dropdown') as HTMLElement;
    const isOpen = dropdown.getAttribute('aria-hidden') === 'false';
    
    if (isOpen) {
      this.closeDropdown();
    } else {
      this.openDropdown();
    }
  }

  private openDropdown(): void {
    const dropdown = this.container.querySelector('.theme-dropdown') as HTMLElement;
    dropdown.setAttribute('aria-hidden', 'false');
    dropdown.classList.add('open');
  }

  private closeDropdown(): void {
    const dropdown = this.container.querySelector('.theme-dropdown') as HTMLElement;
    dropdown.setAttribute('aria-hidden', 'true');
    dropdown.classList.remove('open');
  }

  private focusFirstOption(): void {
    const firstOption = this.container.querySelector('.theme-option') as HTMLButtonElement;
    firstOption?.focus();
  }

  private focusNextOption(): void {
    const options = Array.from(this.container.querySelectorAll('.theme-option'));
    const currentIndex = options.indexOf(document.activeElement as HTMLButtonElement);
    const nextIndex = (currentIndex + 1) % options.length;
    (options[nextIndex] as HTMLButtonElement).focus();
  }

  private focusPreviousOption(): void {
    const options = Array.from(this.container.querySelectorAll('.theme-option'));
    const currentIndex = options.indexOf(document.activeElement as HTMLButtonElement);
    const previousIndex = (currentIndex - 1 + options.length) % options.length;
    (options[previousIndex] as HTMLButtonElement).focus();
  }

  private quickToggle(): void {
    // Quick toggle between light and dark (skipping auto)
    const newTheme = this.getEffectiveTheme() === 'light' ? 'dark' : 'light';
    this.setTheme(newTheme);
  }

  private setTheme(theme: 'light' | 'dark' | 'auto'): void {
    this.currentTheme = theme;
    localStorage.setItem('theme', theme);
    
    this.applyTheme();
    this.render();
    this.setupEventListeners();
    this.closeDropdown();
    
    // Dispatch custom event
    window.dispatchEvent(new CustomEvent('themeChanged', {
      detail: { theme, effectiveTheme: this.getEffectiveTheme() }
    }));
  }

  private announceToScreenReader(message: string): void {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.style.cssText = `
      position: absolute !important;
      width: 1px !important;
      height: 1px !important;
      padding: 0 !important;
      margin: -1px !important;
      overflow: hidden !important;
      clip: rect(0, 0, 0, 0) !important;
      white-space: nowrap !important;
      border: 0 !important;
    `;
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }

  // Public methods
  public getCurrentTheme(): 'light' | 'dark' | 'auto' {
    return this.currentTheme;
  }

  public getEffectiveThemePublic(): 'light' | 'dark' {
    return this.getEffectiveTheme();
  }

  public setThemePublic(theme: 'light' | 'dark' | 'auto'): void {
    this.setTheme(theme);
  }

  public destroy(): void {
    this.mediaQuery.removeEventListener('change', this.setupMediaQueryListener);
    document.removeEventListener('click', this.closeDropdown);
  }
}

// CSS styles for the theme toggle
const styles = `
.theme-toggle {
  position: relative;
  display: inline-block;
}

.theme-toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--bg-secondary, #f8f9fa);
  border: 1px solid var(--border-color, #dee2e6);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 18px;
}

.theme-toggle-btn:hover {
  background: var(--bg-hover, #e9ecef);
  border-color: var(--border-hover, #adb5bd);
  transform: scale(1.05);
}

.theme-toggle-btn:focus {
  outline: 2px solid var(--focus-color, #007bff);
  outline-offset: 2px;
}

.theme-toggle-btn:active {
  transform: scale(0.95);
}

.theme-icon {
  transition: transform 0.3s ease;
}

.theme-toggle-btn:hover .theme-icon {
  transform: rotate(15deg);
}

.theme-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  min-width: 200px;
  background: var(--bg-primary, #ffffff);
  border: 1px solid var(--border-color, #dee2e6);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  margin-top: 8px;
}

.theme-dropdown.open {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.theme-dropdown-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color, #dee2e6);
}

.dropdown-title {
  font-weight: 600;
  font-size: 14px;
  color: var(--text-primary, #212529);
}

.theme-options {
  padding: 8px 0;
}

.theme-option {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 14px;
  position: relative;
}

.theme-option:hover {
  background: var(--bg-hover, #f8f9fa);
}

.theme-option:focus {
  background: var(--bg-focus, #e3f2fd);
  outline: none;
}

.theme-option.selected {
  background: var(--bg-selected, #e3f2fd);
  font-weight: 500;
}

.option-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.option-label {
  flex: 1;
  font-weight: inherit;
}

.option-description {
  font-size: 12px;
  color: var(--text-muted, #6c757d);
  margin-left: auto;
}

.checkmark {
  color: var(--success-color, #28a745);
  font-weight: bold;
  margin-left: auto;
}

.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Dark theme styles */
.dark-theme .theme-toggle-btn {
  --bg-secondary: #343a40;
  --border-color: #495057;
  --bg-hover: #495057;
  --border-hover: #6c757d;
}

.dark-theme .theme-dropdown {
  --bg-primary: #212529;
  --border-color: #495057;
}

.dark-theme .theme-option:hover {
  --bg-hover: #343a40;
}

.dark-theme .theme-option:focus {
  --bg-focus: #1e3a5f;
}

.dark-theme .theme-option.selected {
  --bg-selected: #1e3a5f;
}

.dark-theme .dropdown-title {
  --text-primary: #f8f9fa;
}

.dark-theme .option-description {
  --text-muted: #adb5bd;
}

/* High contrast mode */
.high-contrast .theme-toggle-btn {
  border: 2px solid;
  background: ButtonFace;
  color: ButtonText;
}

.high-contrast .theme-dropdown {
  border: 2px solid;
  background: Window;
  color: WindowText;
}

.high-contrast .theme-option:focus {
  background: Highlight;
  color: HighlightText;
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .theme-toggle-btn,
  .theme-dropdown,
  .theme-icon {
    transition: none;
  }
  
  .theme-toggle-btn:hover {
    transform: none;
  }
  
  .theme-toggle-btn:hover .theme-icon {
    transform: none;
  }
}

/* Animation for theme transitions */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
  }
}
`;

// Inject styles
if (!document.getElementById('theme-toggle-styles')) {
  const styleSheet = document.createElement('style');
  styleSheet.id = 'theme-toggle-styles';
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}
