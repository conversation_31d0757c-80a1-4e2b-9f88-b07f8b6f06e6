import { describe, it, expect, beforeEach, vi } from 'vitest';
import { CalculatorService } from '../src/services/calculator.service';
import { ValidationService } from '../src/services/validation.service';
import { CurrencyService } from '../src/services/currency.service';
import type { CalculatorConfig, Room } from '../src/types/calculator';

// Mock dependencies
vi.mock('../src/services/market-data.service');
vi.mock('../src/services/currency.service');

describe('CalculatorService', () => {
  let calculatorService: CalculatorService;
  let mockConfig: CalculatorConfig;

  beforeEach(() => {
    mockConfig = {
      city: 'delhi',
      currency: 'INR',
      quality: 'standard',
      dimensions: {
        length: 30,
        width: 40,
        floors: 1,
        unit: 'ft'
      },
      rooms: [
        {
          id: 'bedroom',
          name: 'Bedroom',
          emoji: '🛏️',
          area: 200,
          category: 'essential'
        },
        {
          id: 'bathroom',
          name: 'Bathroom',
          emoji: '🚿',
          area: 100,
          category: 'essential'
        }
      ]
    };

    calculatorService = new CalculatorService(mockConfig);
  });

  describe('Configuration Management', () => {
    it('should initialize with provided config', () => {
      const config = calculatorService.getConfig();
      expect(config).toEqual(mockConfig);
    });

    it('should update dimensions correctly', () => {
      const newDimensions = {
        length: 50,
        width: 60,
        floors: 2,
        unit: 'm' as const
      };

      calculatorService.updateDimensions(newDimensions);
      const config = calculatorService.getConfig();
      
      expect(config.dimensions).toEqual(newDimensions);
    });

    it('should update quality level correctly', () => {
      calculatorService.updateQuality('luxury');
      const config = calculatorService.getConfig();
      
      expect(config.quality).toBe('luxury');
    });

    it('should update city correctly', () => {
      calculatorService.updateCity('mumbai');
      const config = calculatorService.getConfig();
      
      expect(config.city).toBe('mumbai');
    });

    it('should update currency correctly', () => {
      calculatorService.updateCurrency('USD');
      const config = calculatorService.getConfig();
      
      expect(config.currency).toBe('USD');
    });

    it('should update rooms correctly', () => {
      const newRooms: Room[] = [
        {
          id: 'kitchen',
          name: 'Kitchen',
          emoji: '🍳',
          area: 150,
          category: 'essential'
        }
      ];

      calculatorService.updateRooms(newRooms);
      const config = calculatorService.getConfig();
      
      expect(config.rooms).toEqual(newRooms);
    });
  });

  describe('Calculation Logic', () => {
    it('should calculate total area correctly', () => {
      // Access private method through type assertion for testing
      const totalArea = (calculatorService as any).calculateTotalArea();
      expect(totalArea).toBe(300); // 200 + 100
    });

    it('should calculate plot area correctly', () => {
      const plotArea = (calculatorService as any).calculatePlotArea();
      expect(plotArea).toBe(1200); // 30 * 40 * 1
    });

    it('should apply quality multiplier correctly', () => {
      const standardMultiplier = (calculatorService as any).getQualityMultiplier();
      expect(standardMultiplier).toBe(1.0);

      calculatorService.updateQuality('premium');
      const premiumMultiplier = (calculatorService as any).getQualityMultiplier();
      expect(premiumMultiplier).toBe(1.25);

      calculatorService.updateQuality('luxury');
      const luxuryMultiplier = (calculatorService as any).getQualityMultiplier();
      expect(luxuryMultiplier).toBe(1.5);
    });
  });

  describe('Validation', () => {
    it('should validate configuration correctly', async () => {
      const result = await calculatorService.calculate();
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect invalid dimensions', async () => {
      calculatorService.updateDimensions({
        length: -10,
        width: 40,
        floors: 1,
        unit: 'ft'
      });

      const result = await calculatorService.calculate();
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.field === 'length')).toBe(true);
    });

    it('should detect missing city', async () => {
      calculatorService.updateCity('');
      
      const result = await calculatorService.calculate();
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.field === 'city')).toBe(true);
    });

    it('should detect missing currency', async () => {
      calculatorService.updateCurrency('' as any);
      
      const result = await calculatorService.calculate();
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.field === 'currency')).toBe(true);
    });
  });

  describe('Recommendations', () => {
    it('should generate space efficiency recommendations', () => {
      // Test low efficiency scenario
      calculatorService.updateRooms([
        {
          id: 'small-room',
          name: 'Small Room',
          emoji: '🏠',
          area: 50,
          category: 'essential'
        }
      ]);

      const recommendations = (calculatorService as any).generateRecommendations();
      expect(recommendations.some((rec: string) => 
        rec.includes('space utilization')
      )).toBe(true);
    });

    it('should recommend essential rooms when missing', () => {
      calculatorService.updateRooms([
        {
          id: 'luxury-pool',
          name: 'Pool',
          emoji: '🏊',
          area: 500,
          category: 'luxury'
        }
      ]);

      const recommendations = (calculatorService as any).generateRecommendations();
      expect(recommendations.some((rec: string) => 
        rec.includes('essential rooms')
      )).toBe(true);
    });

    it('should suggest eco-friendly features', () => {
      calculatorService.updateRooms([
        {
          id: 'bedroom',
          name: 'Bedroom',
          emoji: '🛏️',
          area: 200,
          category: 'essential'
        }
      ]);

      const recommendations = (calculatorService as any).generateRecommendations();
      expect(recommendations.some((rec: string) => 
        rec.includes('eco-friendly')
      )).toBe(true);
    });
  });

  describe('Time Estimation', () => {
    it('should calculate construction phases correctly', () => {
      const timeEstimate = (calculatorService as any).calculateTimeEstimate();
      
      expect(timeEstimate.phases).toBeDefined();
      expect(timeEstimate.phases.length).toBeGreaterThan(0);
      expect(timeEstimate.totalDays).toBeGreaterThan(0);
      expect(timeEstimate.totalMonths).toBeGreaterThan(0);
      expect(timeEstimate.criticalPath).toBeDefined();
    });

    it('should adjust time based on quality level', () => {
      const standardTime = (calculatorService as any).calculateTimeEstimate();
      
      calculatorService.updateQuality('luxury');
      const luxuryTime = (calculatorService as any).calculateTimeEstimate();
      
      expect(luxuryTime.totalDays).toBeGreaterThan(standardTime.totalDays);
    });

    it('should scale time with area', () => {
      const smallAreaTime = (calculatorService as any).calculateTimeEstimate();
      
      calculatorService.updateDimensions({
        length: 60,
        width: 80,
        floors: 2,
        unit: 'ft'
      });
      
      const largeAreaTime = (calculatorService as any).calculateTimeEstimate();
      expect(largeAreaTime.totalDays).toBeGreaterThan(smallAreaTime.totalDays);
    });
  });

  describe('Error Handling', () => {
    it('should handle calculation errors gracefully', async () => {
      // Mock a service to throw an error
      vi.spyOn(calculatorService as any, 'validateInputs').mockRejectedValue(
        new Error('Validation failed')
      );

      const result = await calculatorService.calculate();
      expect(result.isValid).toBe(false);
      expect(result.errors).toBeDefined();
    });

    it('should handle invalid room data', () => {
      const invalidRooms: Room[] = [
        {
          id: '',
          name: '',
          emoji: '',
          area: -100,
          category: 'essential'
        }
      ];

      calculatorService.updateRooms(invalidRooms);
      
      // Should not crash and should handle gracefully
      expect(() => {
        (calculatorService as any).calculateTotalArea();
      }).not.toThrow();
    });
  });
});

describe('ValidationService', () => {
  let validationService: ValidationService;

  beforeEach(() => {
    validationService = new ValidationService();
  });

  describe('Dimension Validation', () => {
    it('should validate correct dimensions', () => {
      const dimensions = {
        length: 30,
        width: 40,
        floors: 2,
        unit: 'ft' as const
      };

      const errors = validationService.validateDimensions(dimensions);
      expect(errors).toHaveLength(0);
    });

    it('should reject negative dimensions', () => {
      const dimensions = {
        length: -10,
        width: 40,
        floors: 1,
        unit: 'ft' as const
      };

      const errors = validationService.validateDimensions(dimensions);
      expect(errors.some(error => error.field === 'length')).toBe(true);
    });

    it('should reject zero dimensions', () => {
      const dimensions = {
        length: 0,
        width: 40,
        floors: 1,
        unit: 'ft' as const
      };

      const errors = validationService.validateDimensions(dimensions);
      expect(errors.some(error => error.field === 'length')).toBe(true);
    });

    it('should reject excessive dimensions', () => {
      const dimensions = {
        length: 15000,
        width: 40,
        floors: 1,
        unit: 'ft' as const
      };

      const errors = validationService.validateDimensions(dimensions);
      expect(errors.some(error => error.field === 'length')).toBe(true);
    });

    it('should reject invalid floor count', () => {
      const dimensions = {
        length: 30,
        width: 40,
        floors: 0,
        unit: 'ft' as const
      };

      const errors = validationService.validateDimensions(dimensions);
      expect(errors.some(error => error.field === 'floors')).toBe(true);
    });

    it('should reject too many floors', () => {
      const dimensions = {
        length: 30,
        width: 40,
        floors: 15,
        unit: 'ft' as const
      };

      const errors = validationService.validateDimensions(dimensions);
      expect(errors.some(error => error.field === 'floors')).toBe(true);
    });

    it('should reject invalid units', () => {
      const dimensions = {
        length: 30,
        width: 40,
        floors: 1,
        unit: 'invalid' as any
      };

      const errors = validationService.validateDimensions(dimensions);
      expect(errors.some(error => error.field === 'unit')).toBe(true);
    });
  });

  describe('Room Validation', () => {
    const validDimensions = {
      length: 30,
      width: 40,
      floors: 1,
      unit: 'ft' as const
    };

    it('should validate correct rooms', () => {
      const rooms: Room[] = [
        {
          id: 'bedroom',
          name: 'Bedroom',
          emoji: '🛏️',
          area: 200,
          category: 'essential'
        }
      ];

      const errors = validationService.validateRooms(rooms, validDimensions);
      expect(errors).toHaveLength(0);
    });

    it('should reject empty room list', () => {
      const errors = validationService.validateRooms([], validDimensions);
      expect(errors.some(error => error.field === 'rooms')).toBe(true);
    });

    it('should reject rooms with excessive total area', () => {
      const rooms: Room[] = [
        {
          id: 'huge-room',
          name: 'Huge Room',
          emoji: '🏠',
          area: 10000,
          category: 'essential'
        }
      ];

      const errors = validationService.validateRooms(rooms, validDimensions);
      expect(errors.some(error => error.field === 'rooms')).toBe(true);
    });

    it('should warn about missing essential rooms', () => {
      const rooms: Room[] = [
        {
          id: 'luxury-pool',
          name: 'Pool',
          emoji: '🏊',
          area: 200,
          category: 'luxury'
        }
      ];

      const errors = validationService.validateRooms(rooms, validDimensions);
      expect(errors.some(error => error.code === 'MISSING_ESSENTIAL_ROOM')).toBe(true);
    });
  });

  describe('Individual Room Validation', () => {
    it('should validate correct room', () => {
      const room: Room = {
        id: 'bedroom',
        name: 'Bedroom',
        emoji: '🛏️',
        area: 200,
        category: 'essential'
      };

      const errors = validationService.validateRoom(room, 0);
      expect(errors).toHaveLength(0);
    });

    it('should reject room without ID', () => {
      const room: Room = {
        id: '',
        name: 'Bedroom',
        emoji: '🛏️',
        area: 200,
        category: 'essential'
      };

      const errors = validationService.validateRoom(room, 0);
      expect(errors.some(error => error.code === 'MISSING_ROOM_ID')).toBe(true);
    });

    it('should reject room without name', () => {
      const room: Room = {
        id: 'bedroom',
        name: '',
        emoji: '🛏️',
        area: 200,
        category: 'essential'
      };

      const errors = validationService.validateRoom(room, 0);
      expect(errors.some(error => error.code === 'MISSING_ROOM_NAME')).toBe(true);
    });

    it('should reject room with invalid area', () => {
      const room: Room = {
        id: 'bedroom',
        name: 'Bedroom',
        emoji: '🛏️',
        area: -100,
        category: 'essential'
      };

      const errors = validationService.validateRoom(room, 0);
      expect(errors.some(error => error.code === 'INVALID_ROOM_AREA')).toBe(true);
    });

    it('should reject room with excessive area', () => {
      const room: Room = {
        id: 'bedroom',
        name: 'Bedroom',
        emoji: '🛏️',
        area: 15000,
        category: 'essential'
      };

      const errors = validationService.validateRoom(room, 0);
      expect(errors.some(error => error.code === 'ROOM_AREA_TOO_LARGE')).toBe(true);
    });

    it('should validate minimum area constraints', () => {
      const room: Room = {
        id: 'bedroom',
        name: 'Bedroom',
        emoji: '🛏️',
        area: 50,
        category: 'essential',
        minArea: 100
      };

      const errors = validationService.validateRoom(room, 0);
      expect(errors.some(error => error.code === 'ROOM_AREA_TOO_SMALL')).toBe(true);
    });

    it('should validate maximum area constraints', () => {
      const room: Room = {
        id: 'bedroom',
        name: 'Bedroom',
        emoji: '🛏️',
        area: 300,
        category: 'essential',
        maxArea: 250
      };

      const errors = validationService.validateRoom(room, 0);
      expect(errors.some(error => error.code === 'ROOM_AREA_TOO_LARGE')).toBe(true);
    });

    it('should reject invalid category', () => {
      const room: Room = {
        id: 'bedroom',
        name: 'Bedroom',
        emoji: '🛏️',
        area: 200,
        category: 'invalid' as any
      };

      const errors = validationService.validateRoom(room, 0);
      expect(errors.some(error => error.code === 'INVALID_ROOM_CATEGORY')).toBe(true);
    });
  });

  describe('Utility Validations', () => {
    it('should validate currency correctly', () => {
      const validErrors = validationService.validateCurrency('USD');
      expect(validErrors).toHaveLength(0);

      const invalidErrors = validationService.validateCurrency('INVALID');
      expect(invalidErrors.some(error => error.code === 'INVALID_CURRENCY')).toBe(true);

      const missingErrors = validationService.validateCurrency('');
      expect(missingErrors.some(error => error.code === 'MISSING_CURRENCY')).toBe(true);
    });

    it('should validate city correctly', () => {
      const validErrors = validationService.validateCity('delhi');
      expect(validErrors).toHaveLength(0);

      const missingErrors = validationService.validateCity('');
      expect(missingErrors.some(error => error.code === 'MISSING_CITY')).toBe(true);
    });

    it('should validate quality correctly', () => {
      const validErrors = validationService.validateQuality('premium');
      expect(validErrors).toHaveLength(0);

      const invalidErrors = validationService.validateQuality('invalid');
      expect(invalidErrors.some(error => error.code === 'INVALID_QUALITY')).toBe(true);

      const missingErrors = validationService.validateQuality('');
      expect(missingErrors.some(error => error.code === 'MISSING_QUALITY')).toBe(true);
    });
  });

  describe('Static Utility Methods', () => {
    it('should validate numeric input correctly', () => {
      expect(ValidationService.validateNumericInput('123')).toBe(true);
      expect(ValidationService.validateNumericInput('123.45')).toBe(true);
      expect(ValidationService.validateNumericInput('abc')).toBe(false);
      expect(ValidationService.validateNumericInput('')).toBe(false);
      expect(ValidationService.validateNumericInput('50', 0, 100)).toBe(true);
      expect(ValidationService.validateNumericInput('150', 0, 100)).toBe(false);
      expect(ValidationService.validateNumericInput('-10', 0, 100)).toBe(false);
    });

    it('should validate email correctly', () => {
      expect(ValidationService.validateEmail('<EMAIL>')).toBe(true);
      expect(ValidationService.validateEmail('<EMAIL>')).toBe(true);
      expect(ValidationService.validateEmail('invalid-email')).toBe(false);
      expect(ValidationService.validateEmail('test@')).toBe(false);
      expect(ValidationService.validateEmail('@domain.com')).toBe(false);
    });

    it('should validate phone number correctly', () => {
      expect(ValidationService.validatePhoneNumber('+1234567890')).toBe(true);
      expect(ValidationService.validatePhoneNumber('1234567890')).toBe(true);
      expect(ValidationService.validatePhoneNumber('+****************')).toBe(true);
      expect(ValidationService.validatePhoneNumber('abc')).toBe(false);
      expect(ValidationService.validatePhoneNumber('')).toBe(false);
    });

    it('should sanitize input correctly', () => {
      expect(ValidationService.sanitizeInput('  hello world  ')).toBe('hello world');
      expect(ValidationService.sanitizeInput('<script>alert("xss")</script>')).not.toContain('<script>');
    });
  });
});
