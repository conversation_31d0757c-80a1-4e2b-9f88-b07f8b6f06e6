# 🏗️ Advanced Construction Cost Calculator

A comprehensive, modern web application for calculating construction costs with global support, real-time pricing, and advanced features.

## ✨ Features

### 🎯 Core Functionality
- **Interactive Floor Planning**: Visual drag-and-drop room placement system
- **Multi-City Support**: Cost calculations for cities worldwide
- **Quality Tiers**: Standard, Premium, and Luxury construction options
- **Comprehensive Room Types**: 40+ room types across 5 categories
- **Multi-Currency Support**: Real-time currency conversion
- **Visual Analytics**: Interactive charts and cost breakdowns

### 🌍 Global Accessibility
- **Internationalization (i18n)**: Support for 10+ languages
- **Regional Pricing**: Localized material and labor costs
- **Currency Conversion**: Real-time exchange rates
- **Cultural Adaptations**: Region-specific room types and regulations

### 🚀 Modern Technology
- **Progressive Web App (PWA)**: Installable, offline-capable
- **TypeScript**: Type-safe development
- **Modular Architecture**: Clean, maintainable codebase
- **Performance Optimized**: Lazy loading, virtual scrolling, debouncing
- **Security First**: Input validation, CSP, XSS protection

### ♿ Accessibility & UX
- **WCAG 2.1 AA Compliant**: Full accessibility support
- **Keyboard Navigation**: Complete keyboard accessibility
- **Screen Reader Support**: ARIA labels and live regions
- **High Contrast Mode**: Enhanced visibility options
- **Responsive Design**: Mobile-first approach

## 🛠️ Technology Stack

### Frontend
- **TypeScript** - Type-safe JavaScript
- **Vite** - Fast build tool and dev server
- **Chart.js** - Interactive data visualization
- **Fabric.js** - Canvas manipulation for floor plans
- **Three.js** - 3D visualization capabilities

### Internationalization
- **i18next** - Comprehensive i18n framework
- **Browser Language Detection** - Automatic locale detection
- **Dynamic Loading** - Lazy-loaded translation files

### Performance & PWA
- **Service Workers** - Offline functionality
- **IndexedDB** - Client-side data storage
- **Web App Manifest** - PWA capabilities
- **Performance Monitoring** - Core Web Vitals tracking

### Security
- **Content Security Policy (CSP)** - XSS protection
- **DOMPurify** - HTML sanitization
- **Input Validation** - Comprehensive data validation
- **Rate Limiting** - API abuse prevention

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

```bash
# Clone the repository
git clone https://github.com/your-username/construction-calculator.git
cd construction-calculator

# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### Development Commands

```bash
# Development
npm run dev              # Start dev server
npm run build           # Build for production
npm run preview         # Preview production build

# Testing
npm run test            # Run unit tests
npm run test:ui         # Run tests with UI
npm run test:coverage   # Generate coverage report

# Code Quality
npm run lint            # Lint code
npm run lint:fix        # Fix linting issues
npm run format          # Format code with Prettier
npm run type-check      # TypeScript type checking

# Analysis
npm run analyze         # Bundle size analysis
```

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── Calculator.ts    # Main calculator component
│   ├── FloorPlan.ts     # Interactive floor planning
│   ├── CostBreakdown.ts # Cost analysis and charts
│   ├── TimeEstimate.ts  # Construction timeline
│   ├── LanguageSwitcher.ts # Language selection
│   └── ThemeToggle.ts   # Dark/light theme toggle
├── services/            # Business logic services
│   ├── calculator.service.ts    # Core calculation logic
│   ├── validation.service.ts    # Input validation
│   ├── currency.service.ts      # Currency conversion
│   └── market-data.service.ts   # Market data fetching
├── utils/               # Utility functions
│   ├── performance.ts   # Performance monitoring
│   ├── security.ts      # Security utilities
│   └── accessibility.ts # Accessibility helpers
├── types/               # TypeScript type definitions
│   └── calculator.ts    # Core type definitions
├── i18n/                # Internationalization
│   ├── index.ts         # i18n configuration
│   └── locales/         # Translation files
├── config/              # Configuration files
│   └── regions.ts       # Regional settings
├── styles/              # CSS styles
│   └── main.css         # Main stylesheet
└── main.ts              # Application entry point

tests/                   # Test files
public/                  # Static assets
├── locales/             # Translation files
└── icons/               # PWA icons
```

## 🌐 Internationalization

### Supported Languages
- 🇺🇸 English
- 🇮🇳 हिन्दी (Hindi)
- 🇪🇸 Español (Spanish)
- 🇫🇷 Français (French)
- 🇩🇪 Deutsch (German)
- 🇨🇳 中文 (Chinese)
- 🇯🇵 日本語 (Japanese)
- 🇸🇦 العربية (Arabic)
- 🇧🇷 Português (Portuguese)
- 🇷🇺 Русский (Russian)

### Adding New Languages

1. Create translation files in `public/locales/[lang]/`
2. Add language to `SUPPORTED_LANGUAGES` in `src/i18n/index.ts`
3. Update language switcher component

```typescript
// Example: Adding Italian support
// public/locales/it/common.json
{
  "app": {
    "title": "Calcolatore Avanzato dei Costi di Costruzione"
  }
}
```

## 🏗️ Architecture

### Component Architecture
- **Modular Design**: Self-contained, reusable components
- **Event-Driven**: Loose coupling through custom events
- **Type Safety**: Full TypeScript coverage
- **Performance**: Optimized rendering and memory usage

### Service Layer
- **Calculator Service**: Core business logic
- **Validation Service**: Input validation and sanitization
- **Currency Service**: Real-time exchange rates
- **Market Data Service**: Construction cost data

### State Management
- **Local State**: Component-level state management
- **Configuration**: Centralized app configuration
- **Persistence**: Local storage with encryption
- **Caching**: Intelligent data caching strategies

## 🔒 Security Features

### Input Validation
- **XSS Prevention**: HTML sanitization
- **SQL Injection**: Parameterized queries
- **CSRF Protection**: Token-based validation
- **Rate Limiting**: API abuse prevention

### Content Security Policy
```javascript
// Strict CSP implementation
"default-src 'self'; script-src 'self' 'nonce-{random}'"
```

### Data Protection
- **Encryption**: Sensitive data encryption
- **Anonymization**: PII data anonymization
- **Secure Storage**: Encrypted local storage
- **Privacy**: GDPR compliance ready

## ♿ Accessibility Features

### WCAG 2.1 AA Compliance
- **Keyboard Navigation**: Full keyboard support
- **Screen Readers**: ARIA labels and descriptions
- **Color Contrast**: High contrast mode
- **Focus Management**: Logical focus flow

### Assistive Technologies
- **Screen Reader Support**: NVDA, JAWS, VoiceOver
- **Voice Control**: Dragon NaturallySpeaking
- **Switch Navigation**: Single-switch support
- **Magnification**: Screen magnifier compatibility

## 📊 Performance

### Core Web Vitals
- **LCP**: < 2.5s (Largest Contentful Paint)
- **FID**: < 100ms (First Input Delay)
- **CLS**: < 0.1 (Cumulative Layout Shift)

### Optimization Techniques
- **Code Splitting**: Dynamic imports
- **Lazy Loading**: On-demand resource loading
- **Virtual Scrolling**: Large list optimization
- **Debouncing**: Input optimization
- **Caching**: Intelligent cache strategies

## 🧪 Testing

### Test Coverage
- **Unit Tests**: Component and service testing
- **Integration Tests**: End-to-end workflows
- **Accessibility Tests**: A11y compliance
- **Performance Tests**: Core Web Vitals

### Running Tests
```bash
# Run all tests
npm run test

# Run with coverage
npm run test:coverage

# Run specific test file
npm run test calculator.test.ts

# Run tests in watch mode
npm run test:watch
```

## 🚀 Deployment

### Build Process
```bash
# Production build
npm run build

# Analyze bundle size
npm run analyze

# Type checking
npm run type-check
```

### Environment Variables
```env
VITE_API_BASE_URL=https://api.constructcalc.com
VITE_CURRENCY_API_KEY=your_api_key
VITE_ANALYTICS_ID=your_analytics_id
```

### PWA Deployment
- **Service Worker**: Automatic caching
- **App Manifest**: Installation prompts
- **Offline Support**: Core functionality offline
- **Update Notifications**: Automatic updates

## 🤝 Contributing

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Ensure all tests pass
6. Submit a pull request

### Code Standards
- **TypeScript**: Strict type checking
- **ESLint**: Code linting
- **Prettier**: Code formatting
- **Conventional Commits**: Commit message format

### Pull Request Process
1. Update documentation
2. Add/update tests
3. Ensure CI passes
4. Request code review
5. Address feedback

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Chart.js** - Data visualization
- **i18next** - Internationalization
- **Vite** - Build tooling
- **TypeScript** - Type safety
- **Contributors** - Community support

## 📞 Support

- **Documentation**: [Wiki](https://github.com/your-username/construction-calculator/wiki)
- **Issues**: [GitHub Issues](https://github.com/your-username/construction-calculator/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/construction-calculator/discussions)
- **Email**: <EMAIL>

---

**Built with ❤️ for the global construction community**
