import type { RegionConfig, AppConfig } from '@/types/calculator';

export const REGIONS: Record<string, RegionConfig> = {
  'asia-south': {
    id: 'asia-south',
    name: 'South Asia',
    countries: ['IN', 'PK', 'BD', 'LK', 'NP', 'BT', 'MV'],
    defaultCurrency: 'INR',
    apiEndpoint: 'https://api-asia.constructcalc.com',
    supportedLanguages: ['en', 'hi', 'bn', 'ta', 'te', 'ur'],
    cities: [
      {
        id: 'delhi',
        name: 'Delhi NCR',
        country: 'India',
        region: 'asia-south',
        materials: [],
        labor: [],
        baseRates: { standard: 900, premium: 1100, luxury: 1300 }
      },
      {
        id: 'mumbai',
        name: 'Mumbai',
        country: 'India',
        region: 'asia-south',
        materials: [],
        labor: [],
        baseRates: { standard: 950, premium: 1200, luxury: 1500 }
      },
      {
        id: 'bangalore',
        name: 'Bangalore',
        country: 'India',
        region: 'asia-south',
        materials: [],
        labor: [],
        baseRates: { standard: 850, premium: 1050, luxury: 1250 }
      },
      {
        id: 'hyderabad',
        name: 'Hyderabad',
        country: 'India',
        region: 'asia-south',
        materials: [],
        labor: [],
        baseRates: { standard: 800, premium: 1000, luxury: 1200 }
      },
      {
        id: 'chandigarh',
        name: 'Chandigarh',
        country: 'India',
        region: 'asia-south',
        materials: [],
        labor: [],
        baseRates: { standard: 1000, premium: 1200, luxury: 1600 }
      }
    ]
  },
  'north-america': {
    id: 'north-america',
    name: 'North America',
    countries: ['US', 'CA', 'MX'],
    defaultCurrency: 'USD',
    apiEndpoint: 'https://api-na.constructcalc.com',
    supportedLanguages: ['en', 'es', 'fr'],
    cities: [
      {
        id: 'new-york',
        name: 'New York',
        country: 'United States',
        region: 'north-america',
        materials: [],
        labor: [],
        baseRates: { standard: 150, premium: 200, luxury: 300 }
      },
      {
        id: 'los-angeles',
        name: 'Los Angeles',
        country: 'United States',
        region: 'north-america',
        materials: [],
        labor: [],
        baseRates: { standard: 140, premium: 180, luxury: 250 }
      },
      {
        id: 'chicago',
        name: 'Chicago',
        country: 'United States',
        region: 'north-america',
        materials: [],
        labor: [],
        baseRates: { standard: 120, premium: 160, luxury: 220 }
      },
      {
        id: 'toronto',
        name: 'Toronto',
        country: 'Canada',
        region: 'north-america',
        materials: [],
        labor: [],
        baseRates: { standard: 130, premium: 170, luxury: 240 }
      }
    ]
  },
  'europe': {
    id: 'europe',
    name: 'Europe',
    countries: ['GB', 'DE', 'FR', 'IT', 'ES', 'NL', 'BE', 'AT', 'CH'],
    defaultCurrency: 'EUR',
    apiEndpoint: 'https://api-eu.constructcalc.com',
    supportedLanguages: ['en', 'de', 'fr', 'it', 'es', 'nl'],
    cities: [
      {
        id: 'london',
        name: 'London',
        country: 'United Kingdom',
        region: 'europe',
        materials: [],
        labor: [],
        baseRates: { standard: 180, premium: 240, luxury: 350 }
      },
      {
        id: 'paris',
        name: 'Paris',
        country: 'France',
        region: 'europe',
        materials: [],
        labor: [],
        baseRates: { standard: 160, premium: 220, luxury: 320 }
      },
      {
        id: 'berlin',
        name: 'Berlin',
        country: 'Germany',
        region: 'europe',
        materials: [],
        labor: [],
        baseRates: { standard: 140, premium: 190, luxury: 280 }
      },
      {
        id: 'madrid',
        name: 'Madrid',
        country: 'Spain',
        region: 'europe',
        materials: [],
        labor: [],
        baseRates: { standard: 120, premium: 170, luxury: 250 }
      }
    ]
  },
  'asia-east': {
    id: 'asia-east',
    name: 'East Asia',
    countries: ['CN', 'JP', 'KR', 'TW', 'HK', 'SG'],
    defaultCurrency: 'JPY',
    apiEndpoint: 'https://api-eastasia.constructcalc.com',
    supportedLanguages: ['en', 'zh', 'ja', 'ko'],
    cities: [
      {
        id: 'tokyo',
        name: 'Tokyo',
        country: 'Japan',
        region: 'asia-east',
        materials: [],
        labor: [],
        baseRates: { standard: 20000, premium: 28000, luxury: 40000 }
      },
      {
        id: 'shanghai',
        name: 'Shanghai',
        country: 'China',
        region: 'asia-east',
        materials: [],
        labor: [],
        baseRates: { standard: 800, premium: 1200, luxury: 1800 }
      },
      {
        id: 'seoul',
        name: 'Seoul',
        country: 'South Korea',
        region: 'asia-east',
        materials: [],
        labor: [],
        baseRates: { standard: 180000, premium: 250000, luxury: 350000 }
      },
      {
        id: 'singapore',
        name: 'Singapore',
        country: 'Singapore',
        region: 'asia-east',
        materials: [],
        labor: [],
        baseRates: { standard: 200, premium: 280, luxury: 400 }
      }
    ]
  },
  'oceania': {
    id: 'oceania',
    name: 'Oceania',
    countries: ['AU', 'NZ', 'FJ', 'PG'],
    defaultCurrency: 'AUD',
    apiEndpoint: 'https://api-oceania.constructcalc.com',
    supportedLanguages: ['en'],
    cities: [
      {
        id: 'sydney',
        name: 'Sydney',
        country: 'Australia',
        region: 'oceania',
        materials: [],
        labor: [],
        baseRates: { standard: 180, premium: 240, luxury: 350 }
      },
      {
        id: 'melbourne',
        name: 'Melbourne',
        country: 'Australia',
        region: 'oceania',
        materials: [],
        labor: [],
        baseRates: { standard: 170, premium: 230, luxury: 330 }
      },
      {
        id: 'auckland',
        name: 'Auckland',
        country: 'New Zealand',
        region: 'oceania',
        materials: [],
        labor: [],
        baseRates: { standard: 160, premium: 220, luxury: 320 }
      }
    ]
  }
};

export const APP_CONFIG: AppConfig = {
  version: '2.0.0',
  regions: Object.values(REGIONS),
  defaultRegion: 'asia-south',
  features: {
    ai_suggestions: true,
    real_time_pricing: true,
    three_d_visualization: true,
    export_pdf: true,
    collaboration: false // Premium feature
  },
  limits: {
    maxRooms: 50,
    maxArea: 100000, // sq ft
    maxFloors: 10
  }
};

export class RegionService {
  private static currentRegion: string = APP_CONFIG.defaultRegion;

  static async detectUserRegion(): Promise<string> {
    try {
      // Try to detect region from IP
      const response = await fetch('https://ipapi.co/json/');
      const data = await response.json();
      
      const countryCode = data.country_code;
      
      // Find region by country code
      for (const [regionId, region] of Object.entries(REGIONS)) {
        if (region.countries.includes(countryCode)) {
          this.currentRegion = regionId;
          return regionId;
        }
      }
    } catch (error) {
      console.warn('Failed to detect region:', error);
    }

    // Fallback to browser language detection
    const language = navigator.language.toLowerCase();
    if (language.startsWith('hi') || language.startsWith('bn')) {
      this.currentRegion = 'asia-south';
    } else if (language.startsWith('zh') || language.startsWith('ja')) {
      this.currentRegion = 'asia-east';
    } else if (language.startsWith('es') && language.includes('mx')) {
      this.currentRegion = 'north-america';
    } else if (language.startsWith('en') && language.includes('au')) {
      this.currentRegion = 'oceania';
    }

    return this.currentRegion;
  }

  static getCurrentRegion(): RegionConfig {
    return REGIONS[this.currentRegion] || REGIONS[APP_CONFIG.defaultRegion];
  }

  static setRegion(regionId: string): void {
    if (REGIONS[regionId]) {
      this.currentRegion = regionId;
      localStorage.setItem('selectedRegion', regionId);
    }
  }

  static async loadRegionalData(regionId: string): Promise<any> {
    const region = REGIONS[regionId];
    if (!region) throw new Error(`Region ${regionId} not found`);

    try {
      const [materialsResponse, laborResponse, regulationsResponse] = await Promise.all([
        fetch(`${region.apiEndpoint}/materials`),
        fetch(`${region.apiEndpoint}/labor-rates`),
        fetch(`${region.apiEndpoint}/regulations`)
      ]);

      return {
        materials: materialsResponse.ok ? await materialsResponse.json() : [],
        labor: laborResponse.ok ? await laborResponse.json() : [],
        regulations: regulationsResponse.ok ? await regulationsResponse.json() : {}
      };
    } catch (error) {
      console.warn('Failed to load regional data:', error);
      return { materials: [], labor: [], regulations: {} };
    }
  }

  static getCitiesByRegion(regionId: string): any[] {
    const region = REGIONS[regionId];
    return region ? region.cities : [];
  }

  static getAllCities(): any[] {
    return Object.values(REGIONS).flatMap(region => region.cities);
  }

  static getCityById(cityId: string): any | null {
    for (const region of Object.values(REGIONS)) {
      const city = region.cities.find(c => c.id === cityId);
      if (city) return city;
    }
    return null;
  }

  static getRegionByCityId(cityId: string): RegionConfig | null {
    for (const region of Object.values(REGIONS)) {
      if (region.cities.some(c => c.id === cityId)) {
        return region;
      }
    }
    return null;
  }

  // Initialize region on app start
  static async initialize(): Promise<void> {
    // Check if user has previously selected a region
    const savedRegion = localStorage.getItem('selectedRegion');
    if (savedRegion && REGIONS[savedRegion]) {
      this.currentRegion = savedRegion;
      return;
    }

    // Auto-detect region
    await this.detectUserRegion();
  }
}
