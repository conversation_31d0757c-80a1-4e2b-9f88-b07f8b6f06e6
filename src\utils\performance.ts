// Performance optimization utilities

export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number[]> = new Map();
  private observers: Map<string, PerformanceObserver> = new Map();

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  // Measure function execution time
  static measure<T>(name: string, fn: () => T): T {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    
    PerformanceMonitor.getInstance().recordMetric(name, end - start);
    return result;
  }

  // Measure async function execution time
  static async measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = performance.now();
    const result = await fn();
    const end = performance.now();
    
    PerformanceMonitor.getInstance().recordMetric(name, end - start);
    return result;
  }

  private recordMetric(name: string, duration: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const metrics = this.metrics.get(name)!;
    metrics.push(duration);
    
    // Keep only last 100 measurements
    if (metrics.length > 100) {
      metrics.shift();
    }
  }

  getMetrics(name: string): { avg: number; min: number; max: number; count: number } | null {
    const metrics = this.metrics.get(name);
    if (!metrics || metrics.length === 0) return null;

    return {
      avg: metrics.reduce((sum, val) => sum + val, 0) / metrics.length,
      min: Math.min(...metrics),
      max: Math.max(...metrics),
      count: metrics.length
    };
  }

  getAllMetrics(): Record<string, any> {
    const result: Record<string, any> = {};
    this.metrics.forEach((_, name) => {
      result[name] = this.getMetrics(name);
    });
    return result;
  }

  // Monitor Core Web Vitals
  startCoreWebVitalsMonitoring(): void {
    // Largest Contentful Paint (LCP)
    this.observePerformanceEntry('largest-contentful-paint', (entries) => {
      const lcpEntry = entries[entries.length - 1];
      this.recordMetric('LCP', lcpEntry.startTime);
    });

    // First Input Delay (FID)
    this.observePerformanceEntry('first-input', (entries) => {
      entries.forEach(entry => {
        this.recordMetric('FID', entry.processingStart - entry.startTime);
      });
    });

    // Cumulative Layout Shift (CLS)
    this.observePerformanceEntry('layout-shift', (entries) => {
      let clsValue = 0;
      entries.forEach(entry => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      });
      this.recordMetric('CLS', clsValue);
    });
  }

  private observePerformanceEntry(entryType: string, callback: (entries: any[]) => void): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        callback(list.getEntries());
      });
      
      observer.observe({ entryTypes: [entryType] });
      this.observers.set(entryType, observer);
    }
  }

  disconnect(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
  }
}

// Debounce utility
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
}

// Throttle utility
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// Lazy loading utility
export class LazyLoader {
  private static observer: IntersectionObserver | null = null;
  private static callbacks: Map<Element, () => void> = new Map();

  static observe(element: Element, callback: () => void, options?: IntersectionObserverInit): void {
    if (!this.observer) {
      this.observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const callback = this.callbacks.get(entry.target);
            if (callback) {
              callback();
              this.unobserve(entry.target);
            }
          }
        });
      }, options);
    }

    this.callbacks.set(element, callback);
    this.observer.observe(element);
  }

  static unobserve(element: Element): void {
    if (this.observer) {
      this.observer.unobserve(element);
      this.callbacks.delete(element);
    }
  }

  static disconnect(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.callbacks.clear();
      this.observer = null;
    }
  }
}

// Virtual scrolling for large lists
export class VirtualScroller {
  private container: HTMLElement;
  private itemHeight: number;
  private items: any[];
  private visibleItems: number;
  private scrollTop: number = 0;
  private renderCallback: (items: any[], startIndex: number) => void;

  constructor(
    container: HTMLElement,
    items: any[],
    itemHeight: number,
    renderCallback: (items: any[], startIndex: number) => void
  ) {
    this.container = container;
    this.items = items;
    this.itemHeight = itemHeight;
    this.renderCallback = renderCallback;
    this.visibleItems = Math.ceil(container.clientHeight / itemHeight) + 2; // Buffer

    this.setupScrollListener();
    this.render();
  }

  private setupScrollListener(): void {
    this.container.addEventListener('scroll', throttle(() => {
      this.scrollTop = this.container.scrollTop;
      this.render();
    }, 16)); // ~60fps
  }

  private render(): void {
    const startIndex = Math.floor(this.scrollTop / this.itemHeight);
    const endIndex = Math.min(startIndex + this.visibleItems, this.items.length);
    
    const visibleItems = this.items.slice(startIndex, endIndex);
    this.renderCallback(visibleItems, startIndex);

    // Set container height to maintain scroll position
    this.container.style.height = `${this.items.length * this.itemHeight}px`;
  }

  updateItems(items: any[]): void {
    this.items = items;
    this.render();
  }
}

// Memory management utilities
export class MemoryManager {
  private static cleanupTasks: (() => void)[] = [];

  static addCleanupTask(task: () => void): void {
    this.cleanupTasks.push(task);
  }

  static cleanup(): void {
    this.cleanupTasks.forEach(task => {
      try {
        task();
      } catch (error) {
        console.warn('Cleanup task failed:', error);
      }
    });
    this.cleanupTasks = [];
  }

  static getMemoryUsage(): MemoryInfo | null {
    if ('memory' in performance) {
      return (performance as any).memory;
    }
    return null;
  }

  static monitorMemoryUsage(threshold: number = 50 * 1024 * 1024): void {
    const checkMemory = () => {
      const memory = this.getMemoryUsage();
      if (memory && memory.usedJSHeapSize > threshold) {
        console.warn('High memory usage detected:', memory);
        this.cleanup();
      }
    };

    setInterval(checkMemory, 30000); // Check every 30 seconds
  }
}

// Bundle splitting utility
export async function loadModule<T>(moduleFactory: () => Promise<T>): Promise<T> {
  try {
    return await PerformanceMonitor.measureAsync('module-load', moduleFactory);
  } catch (error) {
    console.error('Failed to load module:', error);
    throw error;
  }
}

// Initialize performance monitoring
export function initializePerformanceMonitoring(): void {
  const monitor = PerformanceMonitor.getInstance();
  monitor.startCoreWebVitalsMonitoring();
  
  // Monitor memory usage
  MemoryManager.monitorMemoryUsage();
  
  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    monitor.disconnect();
    MemoryManager.cleanup();
  });
}
